<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-2xl text-gray-800 leading-tight">
            Loyalty Configuration
        </h2>
    </x-slot>

    <div class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white rounded-xl shadow-lg p-6">
                <form action="{{ route('loyalty_configurations.store') }}" method="POST">
                    @csrf
                    <div class="space-y-6">
                        <!-- Point Name -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Point Name</label>
                            <input type="text" name="point_name" value="{{ $loyaltyConfig->point_name ?? 'Loyalty Point' }}" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" required>
                        </div>

                        <!-- Earning Rate -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Earning Rate</label>
                            <input type="number" step="0.01" name="earning_rate" value="{{ $loyaltyConfig->earning_rate ?? '0.5' }}" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" required>
                        </div>

                        <!-- Spending Rate -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Spending Rate</label>
                            <input type="number" step="0.01" name="spending_rate" value="{{ $loyaltyConfig->spending_rate ?? '1' }}" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" required>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end">
                            <button type="submit" class="inline-flex items-center px-4 py-2.5 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-colors duration-200">
                                Save Configuration
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>