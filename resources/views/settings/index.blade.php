<x-app-layout>
      <x-slot name="header">
          <h2 class="font-semibold text-2xl text-gray-800 leading-tight">
             Settings
          </h2>
      </x-slot>

      <div class="py-8">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <!-- Company Details Card -->
             <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                  <h3 class="font-semibold text-lg text-gray-800 mb-4">Company Details</h3>
                 <form action="{{ route('settings.update.company') }}" method="POST">
                      @csrf
                     <div class="space-y-6">
                         <!-- Company Name -->
                         <div class="mb-6">
                             <label class="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                             <input type="text" name="company_name" value="{{ auth()->user()->company_name }}" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" required>
                         </div>

                          <!-- Google Review Link -->
                         <div class="mb-6">
                              <label class="block text-sm font-medium text-gray-700 mb-2">Google Review Link</label>
                             <input type="url" name="google_review_link" value="{{ auth()->user()->google_review_link }}" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
                          </div>

                         <!-- Submit Button -->
                          <div class="flex justify-end">
                              <button type="submit" class="inline-flex items-center px-4 py-2.5 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-colors duration-200">
                                 Update Details
                             </button>
                          </div>
                     </div>
                 </form>
              </div>

              <!-- Change Password Card -->
             <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                 <h3 class="font-semibold text-lg text-gray-800 mb-4">Change Password</h3>
                 <form action="{{ route('settings.update.password') }}" method="POST">
                        @csrf
                        <div class="space-y-6">
                            <!-- Current Password -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                                <input type="password" name="current_password" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" required>
                            </div>

                            <!-- New Password -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                               <input type="password" name="new_password" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" required>
                            </div>

                           <!-- Confirm New Password -->
                           <div class="mb-6">
                               <label class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                                <input type="password" name="new_password_confirmation" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" required>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end">
                                <button type="submit" class="inline-flex items-center px-4 py-2.5 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-colors duration-200">
                                    Change Password
                                </button>
                            </div>
                       </div>
                    </form>
                </div>
                 <!-- Phone Number Card -->
                <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                  <h3 class="font-semibold text-lg text-gray-800 mb-4">Phone Number</h3>
                   <form action="{{ route('settings.update.phone') }}" method="POST">
                        @csrf
                         <div class="mb-4">
                            <label for="phone_number" class="block text-gray-700 text-sm font-bold mb-2">Phone Number:</label>
                            <input type="text" name="phone_number" id="phone_number" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" value="{{ auth()->user()->phone_number }}" required>
                        </div>
                        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                           Update Phone Number
                        </button>
                   </form>
              </div>

                <!-- Messages Configuration Card -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="font-semibold text-lg text-gray-800 mb-4">Messages Configuration</h3>
                    <form action="{{ route('settings.update.message') }}" method="POST">
                        @csrf
                        <div class="space-y-6">
                            <!-- Purchase Message -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Purchase Message (add {link} for google review link)</label>
                                <textarea name="purchase_message" rows="3" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">{{ auth()->user()->purchase_message }}</textarea>
                            </div>

                            <!-- Birthday Message -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Birthday Message</label>
                                <textarea name="birthday_message" rows="3" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">{{ auth()->user()->birthday_message }}</textarea>
                            </div>

                            <!-- Anniversary Message -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Anniversary Message</label>
                                <textarea name="anniversary_message" rows="3" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">{{ auth()->user()->anniversary_message }}</textarea>
                           </div>

                            <!-- Submit Button -->
                           <div class="flex justify-end">
                                <button type="submit" class="inline-flex items-center px-4 py-2.5 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-colors duration-200">
                                    Update Messages
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
     </x-app-layout>