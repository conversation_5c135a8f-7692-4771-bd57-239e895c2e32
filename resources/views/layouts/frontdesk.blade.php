<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
     <meta charset="utf-8">
     <meta name="viewport" content="width=device-width, initial-scale=1">
     <meta name="csrf-token" content="{{ csrf_token() }}">

     <title>{{ config('app.name', 'Laravel') }} - Front Desk</title>

    <!-- Favicon -->
    <link rel="icon" href="{{ asset('images/icon.png') }}" type="image/png">

     <!-- Fonts -->
     <link rel="preconnect" href="https://fonts.bunny.net">
     <link href="https://fonts.bunny.net/css?family=inter:300,400,500,600,700&display=swap" rel="stylesheet" />

     <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <style>
         body {
             font-family: 'Inter', sans-serif;
         }
         .transition-all {
             transition: all 0.3s ease;
         }
         .notification {
            transition: transform 0.3s ease, opacity 0.2s ease;
             transform: translateY(0);
             opacity: 1;
         }
         .notification.hide {
             transform: translateY(-20px);
            opacity: 0;
        }
     </style>
</head>
<body class="font-sans antialiased bg-gray-50">
    <div class="min-h-screen flex flex-col">
        <!-- Top Bar -->
        <nav class="bg-white shadow-sm sticky top-0 z-10">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center space-x-4">
                        <img src="{{ asset('images/logo.jpg') }}" alt="{{ config('app.name', 'Laravel') }}" class="h-10 w-auto rounded-md shadow-sm">
                       <a href="{{ route('frontdesk.dashboard') }}" class="text-xl font-semibold text-gray-800 tracking-tight">
                        </a>
                    </div>
                    <a href="{{ route('frontdesk.dashboard') }}" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-medium text-sm text-white hover:bg-indigo-700 transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                           <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                        Dashboard
                    </a>
                </div>
            </div>
        </nav>
        
        <!-- Page Content -->
        <main class="flex-grow py-8 flex items-center justify-center">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
                <!-- Notifications -->
                @if (session('success'))
                <div class="notification max-w-lg mx-auto mb-8 rounded-lg bg-green-50 shadow-sm border border-green-100 overflow-hidden transition-all" x-data="{ show: true }" x-show="show" x-init="setTimeout(() => show = false, 4000)">
                    <div class="px-4 py-4 flex items-start">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-3 w-0 flex-1">
                            <p class="text-sm font-medium text-green-800">Success</p>
                           <p class="mt-1 text-sm text-green-700">@yield('success')</p>
                        </div>
                        <div class="ml-4 flex-shrink-0 flex">
                            <button @click="show = false" class="bg-green-50 rounded-md inline-flex text-green-400 hover:text-green-500 focus:outline-none">
                                <span class="sr-only">Close</span>
                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                @endif

                @if ($errors->any())
                 <div class="notification max-w-lg mx-auto mb-8 rounded-lg bg-red-50 shadow-sm border border-red-100 overflow-hidden transition-all" x-data="{ show: true }" x-show="show">
                    <div class="px-4 py-4 flex items-start">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                        </div>
                        <div class="ml-3 w-0 flex-1">
                            <p class="text-sm font-medium text-red-800">Please fix the following errors:</p>
                             <ul class="mt-1 text-sm text-red-700 list-disc pl-5 space-y-1">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                       </div>
                        <div class="ml-4 flex-shrink-0 flex">
                            <button @click="show = false" class="bg-red-50 rounded-md inline-flex text-red-400 hover:text-red-500 focus:outline-none">
                                <span class="sr-only">Close</span>
                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
               @endif
                
               <!-- Main Content Container -->
                <div class="bg-white shadow-sm rounded-lg overflow-hidden max-w-4xl mx-auto">
                    <div class="px-4 py-5 sm:p-6">
                        @yield('content')
                    </div>
                </div>
                
                <!-- Footer -->
                <footer class="mt-12 mb-6 text-center text-gray-500 text-sm">
                    <p>© {{ date('Y') }} {{ config('app.name', 'Laravel') }} - All rights reserved</p>
                </footer>
            </div>
        </main>
    </div>
    
    <!-- Alpine.js for notifications (add to your app.js bundle if not already included) -->
    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/alpinejs/3.12.0/cdn.min.js"></script>
</body>
</html>