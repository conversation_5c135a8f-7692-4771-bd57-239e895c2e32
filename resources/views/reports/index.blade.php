<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-2xl text-gray-800 leading-tight">
            Reports
        </h2>
    </x-slot>

    <div class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="mb-6">
                <form method="GET" class="flex items-center space-x-2">
                    <label for="sort" class="text-sm font-medium text-gray-700">Sort by:</label>
                    <select name="sort" id="sort" class="border rounded-lg px-4 py-2.5 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
                        <option value="created_at" @if(request('sort') == 'created_at') selected @endif>Date</option>
                        <option value="amount" @if(request('sort') == 'amount') selected @endif>Amount</option>
                    </select>
                    <select name="direction" id="direction" class="border rounded-lg px-4 py-2.5 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
                        <option value="asc" @if(request('direction') == 'asc') selected @endif>Ascending</option>
                        <option value="desc" @if(request('direction') == 'desc') selected @endif>Descending</option>
                    </select>
                    <button type="submit" class="inline-flex items-center px-4 py-2.5 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-colors duration-200">
                        Apply Sort
                    </button>
                </form>
            </div>

            <!-- Modern Table -->
            <div class="overflow-hidden rounded-xl border border-gray-200 shadow-sm">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loyalty Points Spent</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($purchases as $purchase)
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{{ $purchase->customer->name }} ({{ $purchase->customer->phone_number }})</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">₹{{ number_format($purchase->amount, 2) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">₹{{ number_format($purchase->discount, 2) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{{ $purchase->loyalty_points_spent }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{{ $purchase->created_at->format('Y-m-d') }}</td>
                            </tr>
                        @empty
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-center" colspan="5">No purchases found</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="mt-6">
                {{ $purchases->appends(request()->except('page'))->links() }}
            </div>
        </div>
    </div>
</x-app-layout>