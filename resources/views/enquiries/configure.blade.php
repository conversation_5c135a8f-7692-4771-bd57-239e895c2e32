<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Configure Enquiry Form
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Add New Question Card -->
            <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100 mb-8">
                <h3 class="font-semibold text-lg mb-4">Add New Question</h3>
                <form action="{{ route('enquiries.questions.store') }}" method="POST">
                    @csrf
                    <div class="mb-6">
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Title:</label>
                        <input type="text" name="title" id="title" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white text-gray-900 placeholder-gray-400" placeholder="Enter question title" required>
                    </div>
                    <div class="mb-6">
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Type:</label>
                        <select name="type" id="type" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white text-gray-900" required>
                            <option value="text">Text</option>
                            <option value="date">Date</option>
                            <option value="radio">Radio</option>
                            <option value="checkbox">Checkbox</option>
                        </select>
                    </div>
                    <div id="options-container" class="mb-6 hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Options (for Radio/Checkbox, one per line):</label>
                        <div id="option-fields">
                            <div class="mb-2">
                                <input type="text" name="options[]" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white text-gray-900 placeholder-gray-400" placeholder="Enter option">
                            </div>
                        </div>
                        <button type="button" id="add-option" class="inline-flex items-center px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium rounded-lg text-sm transition-colors duration-200">
                            Add Option
                        </button>
                    </div>
                    <button type="submit" class="inline-flex items-center px-4 py-2.5 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-colors duration-200">
                        Add Question
                    </button>
                </form>
            </div>

            <!-- Existing Questions Card -->
            <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                <h3 class="font-semibold text-lg mb-4">Existing Questions</h3>
                <ul class="space-y-4">
                    @forelse($questions as $question)
                        <li class="bg-gray-100 p-4 rounded-md flex items-center justify-between">
                            <div>{{ $question->title }} ({{ $question->type }})</div>
                            <form action="{{ route('enquiries.questions.destroy', $question) }}" method="POST" onsubmit="return confirm('Are you sure?');">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                            </form>
                        </li>
                    @empty
                        <p class="text-gray-500">No questions configured yet.</p>
                    @endforelse
                </ul>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const typeSelect = document.getElementById('type');
            const optionsContainer = document.getElementById('options-container');
            const optionFields = document.getElementById('option-fields');
            const addOptionButton = document.getElementById('add-option');

            function toggleOptionsVisibility() {
                if (typeSelect.value === 'radio' || typeSelect.value === 'checkbox') {
                    optionsContainer.classList.remove('hidden');
                } else {
                    optionsContainer.classList.add('hidden');
                }
            }

            typeSelect.addEventListener('change', toggleOptionsVisibility);
            toggleOptionsVisibility();

            addOptionButton.addEventListener('click', function() {
                const newOptionField = document.createElement('div');
                newOptionField.classList.add('mb-2');
                newOptionField.innerHTML = `
                    <input type="text" name="options[]" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white text-gray-900 placeholder-gray-400" placeholder="Enter option">
                `;
                optionFields.appendChild(newOptionField);
            });
        });
    </script>
</x-app-layout>