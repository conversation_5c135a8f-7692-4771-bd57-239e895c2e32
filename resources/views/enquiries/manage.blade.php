<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Manage Enquiries
        </h2>
    </x-slot>

    <div class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white rounded-xl shadow-xl p-8 border border-gray-100 transition-all duration-300 hover:shadow-2xl">
                <!-- Add Enquiry Button -->
                <div class="mb-8">
                    <a href="{{ route('enquiries.create') }}" class="inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-indigo-700 focus:ring-4 focus:ring-blue-300 transition-all duration-300 shadow-md hover:shadow-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 01-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                        </svg>
                        Add Enquiry
                    </a>
                </div>

                <!-- Filter Form -->
                <form method="GET" class="mb-8 bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">Filter Options</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="lead_type" class="block text-sm font-medium text-gray-700 mb-2">Filter by Lead Type:</label>
                            <select name="lead_type" id="lead_type" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white text-gray-900 shadow-sm">
                                <option value="">All Leads</option>
                                <option value="hot" {{ request('lead_type') == 'hot' ? 'selected' : '' }}>Hot</option>
                                <option value="mild" {{ request('lead_type') == 'mild' ? 'selected' : '' }}>Mild</option>
                                <option value="cold" {{ request('lead_type') == 'cold' ? 'selected' : '' }}>Cold</option>
                            </select>
                        </div>
                        <div>
                            <label for="sort_by" class="block text-sm font-medium text-gray-700 mb-2">Sort by:</label>
                            <select name="sort_by" id="sort_by" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white text-gray-900 shadow-sm">
                                <option value="newest" {{ request('sort_by') == 'newest' ? 'selected' : '' }}>Newest First</option>
                                <option value="oldest" {{ request('sort_by') == 'oldest' ? 'selected' : '' }}>Oldest First</option>
                            </select>
                        </div>
                         <div>
                            <label for="marketing_team_member_id" class="block text-sm font-medium text-gray-700 mb-2">Assigned To:</label>
                            <select name="marketing_team_member_id" id="marketing_team_member_id" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white text-gray-900 shadow-sm">
                                <option value="">All Team Members</option>
                                @foreach($teamMembers as $member)
                                    <option value="{{ $member->id }}" {{ request('marketing_team_member_id') == $member->id ? 'selected' : '' }}>
                                        {{ $member->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="self-end">
                            <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-indigo-700 focus:ring-4 focus:ring-blue-300 transition-all duration-300 shadow-md hover:shadow-lg">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
                                </svg>
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </form>

                <!-- Enquiries Table -->
                <div class="overflow-hidden rounded-xl border border-gray-200 shadow-lg">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gradient-to-r from-blue-50 to-indigo-50">
                            <tr>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Name</th>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Phone</th>
                                 <th class="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Assigned To</th>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @forelse($enquiries as $enquiry)
                                <tr class="hover:bg-blue-50 transition-colors duration-200">
                                    <td class="px-6 py-5 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white font-semibold text-lg">
                                                {{ substr($enquiry->name, 0, 1) }}
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-md font-medium text-gray-900">{{ $enquiry->name }}</div>
                                                <div class="text-sm text-gray-500">Lead Type: {{ ucfirst($enquiry->lead_type) }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-5 whitespace-nowrap">
                                        <div class="text-md text-gray-900">{{ $enquiry->phone_number }}</div>
                                        <div class="text-sm text-gray-500">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Active
                                            </span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-5 whitespace-nowrap">
                                        @if($enquiry->marketingTeamMember)
                                         {{ $enquiry->marketingTeamMember->name }}
                                        @else
                                        Not Assigned
                                        @endif
                                     </td>
                                    <td class="px-6 py-5 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button class="inline-flex items-center px-3 py-1.5 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors duration-200 details-btn" data-enquiry-id="{{ $enquiry->id }}" onclick="toggleDetails('{{ route('enquiries.show', $enquiry) }}', this)">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                                            </svg>
                                            Details
                                        </button>
                                        <a href="https://wa.me/{{ $enquiry->phone_number }}" class="inline-flex items-center px-3 py-1.5 bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors duration-200" target="_blank">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"/>
                                            </svg>
                                            WhatsApp
                                        </a>
                                        <a href="tel:{{ $enquiry->phone_number }}" class="inline-flex items-center px-3 py-1.5 bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200 transition-colors duration-200">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                            </svg>
                                            Call
                                        </a>

                                        <form action="{{ route('enquiries.convert', $enquiry) }}" method="POST" class="inline-block">
                                            @csrf
                                            <button type="submit" class="inline-flex items-center px-3 py-1.5 bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors duration-200">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                </svg>
                                                Convert
                                            </button>
                                        </form>

                                        <form action="{{ route('enquiries.destroy', $enquiry) }}" method="POST" class="inline-block" onsubmit="return confirm('Are you sure you want to delete this enquiry?');">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="inline-flex items-center px-3 py-1.5 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors duration-200">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                </svg>
                                                Delete
                                            </button>
                                        </form>
                                         <!-- Assign Dropdown -->
                                        <div class="relative inline-block text-left">
                                            <button type="button" class="inline-flex items-center px-3 py-1.5 bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200 transition-colors duration-200 assign-btn" id="assign-dropdown-{{ $enquiry->id }}" aria-haspopup="true" aria-expanded="false">
                                                 <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                                                     <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
                                                 </svg>

                                                Assign
                                            </button>
                                             <div class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden" role="menu" aria-orientation="vertical" aria-labelledby="assign-dropdown-{{ $enquiry->id }}" id="assign-menu-{{$enquiry->id}}">
                                                 <div class="py-1" role="none">
                                                     @foreach($teamMembers as $member)
                                                        <form action="{{ route('enquiries.assign', $enquiry) }}" method="POST">
                                                            @csrf
                                                            <input type="hidden" name="marketing_team_member_id" value="{{ $member->id }}">
                                                                <button type="submit" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 w-full text-left" role="menuitem">{{ $member->name }}</button>
                                                        </form>
                                                     @endforeach
                                                     <form action="{{ route('enquiries.assign', $enquiry) }}" method="POST">
                                                            @csrf
                                                            <input type="hidden" name="marketing_team_member_id" value="">
                                                         <button  type="submit" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 w-full text-left" role="menuitem">Unassign</button>
                                                     </form>
                                                 </div>
                                             </div>
                                         </div>

                                     </td>
                                </tr>
                                 <tr data-enquiry-details="{{ $enquiry->id }}" class="hidden">
                                     <td colspan="4" class="p-6 bg-blue-50 border-t border-b border-blue-100 shadow-inner">
                                         <div class="animate-fade-in duration-300 details-content">
                                             <!-- Enquiry Details will be loaded here -->
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                     <td class="px-6 py-10 text-center whitespace-nowrap text-sm text-gray-500" colspan="4">
                                         <div class="flex flex-col items-center justify-center space-y-3">
                                             <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                             <p class="text-gray-600 text-lg">No enquiries found</p>
                                             <a href="{{ route('enquiries.create') }}" class="inline-flex items-center px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">
                                                 Add your first enquiry
                                             </a>
                                         </div>
                                     </td>
                                </tr>
                            @endforelse
                        </tbody>
                     </table>
                 </div>

                <!-- Pagination -->
                <div class="mt-8">
                    {{ $enquiries->appends(request()->except('page'))->links() }}
                </div>
            </div>
        </div>
    </div>

    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        .dropdown-active {
            display: block !important;
        }
    </style>

     <script>
        //For the assign to dropdown
        document.addEventListener('DOMContentLoaded', function() {
            // Select all assign buttons and dropdown menus
            var assignButtons = document.querySelectorAll('.assign-btn');
            var dropdownMenus = document.querySelectorAll('[id^="assign-menu-"]');

            // Function to close all dropdowns
            function closeAllDropdowns() {
                dropdownMenus.forEach(function(menu) {
                    menu.classList.add('hidden');
                });
            }

            // Add click event to each assign button
            assignButtons.forEach(function(button) {
                button.addEventListener('click', function(event) {
                    event.stopPropagation(); // Prevent the click from immediately propagating to the document

                    // Close all dropdowns
                    closeAllDropdowns();

                    // Get the enquiry ID from the button's ID
                    var enquiryId = this.id.replace('assign-dropdown-', '');

                    // Find the corresponding dropdown menu
                    var menu = document.getElementById('assign-menu-' + enquiryId);

                    // Toggle the 'hidden' class to show/hide the dropdown
                    if (menu) {
                        menu.classList.toggle('hidden');
                    }
                });
            });

            // Close the dropdown if clicked outside of the dropdown
            document.addEventListener('click', function(event) {
                var isClickInsideDropdown = Array.from(assignButtons).some(button => button.contains(event.target)) ||
                                            Array.from(dropdownMenus).some(menu => menu.contains(event.target));
                if (!isClickInsideDropdown) {
                    closeAllDropdowns(); // Close all dropdowns
                }
            });
        });


        //For the enquiry details toggling
        function toggleDetails(route, btn) {
            let detailsRow = btn.closest('tr').nextElementSibling;
            let enquiryId = btn.dataset.enquiryId;
            let detailsContent = detailsRow.querySelector('.details-content');

             if (detailsRow.classList.contains('hidden')) {
                fetch(route)
                    .then(response => response.json())
                    .then(enquiry => {
                        let detailsHtml = '<div class="space-y-4">';

                        // Basic details section
                        detailsHtml += '<div class="bg-white p-5 rounded-lg shadow-sm border border-gray-200">';
                        detailsHtml += '<h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">Enquiry Details</h3>';

                        // Create a flex layout for the data
                         detailsHtml += '<div class="grid grid-cols-1 md:grid-cols-2 gap-4">';

                        // Left column
                        detailsHtml += '<div>';
                         detailsHtml += '<p class="mb-2"><span class="font-medium text-gray-700">Lead Type:</span> <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ' +
                            (enquiry.lead_type === 'hot' ? 'bg-red-100 text-red-800' :
                                enquiry.lead_type === 'mild' ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-blue-100 text-blue-800') +
                            '">' + enquiry.lead_type.charAt(0).toUpperCase() + enquiry.lead_type.slice(1) + '</span></p>';
                        detailsHtml += '</div>';

                        // Right column
                        detailsHtml += '<div>';
                        detailsHtml += '<p class="mb-2"><span class="font-medium text-gray-700">Created:</span> <span class="text-gray-600">' + new Date(enquiry.created_at).toLocaleString() + '</span></p>';
                        detailsHtml += '</div>';

                        detailsHtml += '</div>'; // End grid

                        // Additional notes section
                        if (enquiry.additional_notes) {
                             detailsHtml += '<div class="mt-4 p-4 bg-gray-50 rounded-md border border-gray-200">';
                            detailsHtml += '<h4 class="font-medium text-gray-700 mb-2">Additional Notes:</h4>';
                            detailsHtml += '<p class="text-gray-600 whitespace-pre-line">' + enquiry.additional_notes + '</p>';
                             detailsHtml += '</div>';
                        }

                         detailsHtml += '</div>'; // End basic details section

                        // Responses section
                        if (enquiry.responses && enquiry.responses.length > 0) {
                            detailsHtml += '<div class="bg-white p-5 rounded-lg shadow-sm border border-gray-200">';
                            detailsHtml += '<h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">Questionnaire Responses</h3>';

                            detailsHtml += '<div class="space-y-3">';
                             enquiry.responses.forEach(response => {
                                detailsHtml += '<div class="p-3 bg-gray-50 rounded-md">';
                                detailsHtml += '<p class="font-medium text-gray-700">' + response.question.title + '</p>';
                                detailsHtml += '<p class="mt-1 text-gray-600">' + (response.response || 'N/A') + '</p>';
                                detailsHtml += '</div>';
                             });
                            detailsHtml += '</div>';

                            detailsHtml += '</div>'; // End responses section
                         }

                        // Actions section
                         detailsHtml += '<div class="flex justify-end space-x-3 mt-4">';
                        detailsHtml += '<button class="px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" onclick="toggleDetails(\'' + route + '\', document.querySelector(\'[data-enquiry-id=\\\'' + enquiryId + '\\\']\'))">Close Details</button>';
                         detailsHtml += '</div>';

                        detailsHtml += '</div>'; // End main space-y-4 div

                        detailsContent.innerHTML = detailsHtml;
                        detailsRow.classList.remove('hidden');
                         btn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clip-rule="evenodd" /><path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" /></svg> Hide Details';
                    })
                     .catch(error => console.error('Error:', error));
            } else {
                 detailsRow.classList.add('hidden');
                btn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z" /><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7 9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" /></svg> Details';
             }
        }
    </script>
</x-app-layout>