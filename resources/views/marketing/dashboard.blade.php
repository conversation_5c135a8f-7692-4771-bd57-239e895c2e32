<x-marketing-layout>
    <x-slot name="header">
     <h2 class="font-semibold text-xl text-gray-800 leading-tight">
         Marketing Dashboard
     </h2>
     </x-slot>
         <div class="py-12">
             <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                 <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                     <div class="p-6">
                         <h3 class="text-lg font-medium text-gray-900">Assigned Enquiries</h3>
                         <div class="mt-4">
                          @if($enquiries->isEmpty())
                             <p>No enquiries assigned yet.</p>
                             @else
                             <div class="overflow-x-auto">
                                 <table class="min-w-full divide-y divide-gray-200">
                                     <thead class="bg-gray-50">
                                      <tr>
                                             <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                             <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                                             <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lead Type</th>
                                             <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
 
                                         </tr>
                                      </thead>
                                      <tbody class="bg-white divide-y divide-gray-200">
                                         @foreach($enquiries as $enquiry)
                                           <tr>
                                              <td class="px-6 py-4 whitespace-nowrap">{{ $enquiry->name }}</td>
                                                 <td class="px-6 py-4 whitespace-nowrap">{{ $enquiry->phone_number }}</td>
                                              <td class="px-6 py-4 whitespace-nowrap">{{ ucfirst($enquiry->lead_type) }}</td>
                                               <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    <a href="{{ route('enquiries.show', $enquiry) }}" class="text-indigo-600 hover:text-indigo-900">View</a>
                                                  </td>
                                           </tr>
                                         @endforeach
                                       </tbody>
                                  </table>
                              </div>
                              @endif
                          </div>
                  </div>
                 </div>
             </div>
         </div>
 </x-marketing-layout>