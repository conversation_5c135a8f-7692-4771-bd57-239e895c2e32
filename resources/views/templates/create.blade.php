<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Create Template
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                <form action="{{ route('templates.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf

                    <div class="mb-4">
                        <label for="name" class="block text-gray-700 text-sm font-bold mb-2">Template Name:</label>
                        <input type="text" name="name" id="name" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                    </div>

                    <div class="mb-4">
                        <label for="layout" class="block text-gray-700 text-sm font-bold mb-2">Select Layout:</label>
                        <select name="layout" id="layout" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                            <option value="layout1">Layout 1</option>
                            <option value="layout2">Layout 2</option>
                        </select>
                    </div>

                    <!-- Layout 1 Fields -->
                    <div id="layout1-fields" class="layout-fields">
                        <div class="mb-4">
                            <label for="heading" class="block text-gray-700 text-sm font-bold mb-2">Heading:</label>
                            <input type="text" name="heading" id="heading" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>
                        <div class="mb-4">
                            <label for="subheading" class="block text-gray-700 text-sm font-bold mb-2">Subheading:</label>
                            <input type="text" name="subheading" id="subheading" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>
                        <div class="mb-4">
                            <label for="feature1" class="block text-gray-700 text-sm font-bold mb-2">Feature 1:</label>
                            <input type="text" name="feature1" id="feature1" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>
                        <div class="mb-4">
                            <label for="feature2" class="block text-gray-700 text-sm font-bold mb-2">Feature 2:</label>
                            <input type="text" name="feature2" id="feature2" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>
                        <div class="mb-4">
                            <label for="feature3" class="block text-gray-700 text-sm font-bold mb-2">Feature 3:</label>
                            <input type="text" name="feature3" id="feature3" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>
                        <div class="mb-4">
                            <label for="button_text" class="block text-gray-700 text-sm font-bold mb-2">Button Text:</label>
                            <input type="text" name="button_text" id="button_text" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>
                         <div class="mb-4">
                            <label for="offer_text" class="block text-gray-700 text-sm font-bold mb-2">Offer Text:</label>
                            <input type="text" name="offer_text" id="offer_text" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>
                         <div class="mb-4">
                            <label for="offer_subtext" class="block text-gray-700 text-sm font-bold mb-2">Offer Subtext:</label>
                            <input type="text" name="offer_subtext" id="offer_subtext" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>

                        <div class="mb-4">
                            <label for="image" class="block text-gray-700 text-sm font-bold mb-2">Image:</label>
                            <input type="file" name="image" id="image" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>
                    </div>

                    <!-- Layout 2 Fields -->
                    <div id="layout2-fields" class="layout-fields" style="display: none;">
                      <div class="mb-4">
                            <label for="heading" class="block text-gray-700 text-sm font-bold mb-2">Heading:</label>
                            <input type="text" name="heading"  class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>
                          <div class="mb-4">
                            <label for="description" class="block text-gray-700 text-sm font-bold mb-2">Description:</label>
                            <input type="text" name="description"  class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>
                         <div class="mb-4">
                            <label for="location" class="block text-gray-700 text-sm font-bold mb-2">Location:</label>
                            <input type="text" name="location"  class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>
                      <div class="mb-4">
                            <label for="availability" class="block text-gray-700 text-sm font-bold mb-2">Availability:</label>
                            <input type="text" name="availability"  class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>
                        <div class="mb-4">
                            <label for="button_text" class="block text-gray-700 text-sm font-bold mb-2">Button Text:</label>
                            <input type="text" name="button_text"  class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>
                       
                        <div class="mb-4">
                            <label for="image" class="block text-gray-700 text-sm font-bold mb-2">Image:</label>
                            <input type="file" name="image"  class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>

                    </div>

                    <div class="mb-4">
                        <label for="background_color" class="block text-gray-700 text-sm font-bold mb-2">Background Color:</label>
                        <input type="color" name="background_color" id="background_color" class="shadow appearance-none  rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    </div>


                   <div class="mb-4" id="preview-container">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Preview:</label>
                         <div id="preview-content" class="border p-4">
                            <!-- The selected template preview will be displayed here -->
                         </div>
                    </div>
                      <!-- Hidden input to store the final HTML content -->
                    <input type="hidden" name="content" id="content">


                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">Save Template</button>
                </form>
            </div>
        </div>
    </div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const layoutSelect = document.getElementById('layout');
    const layout1Fields = document.getElementById('layout1-fields');
    const layout2Fields = document.getElementById('layout2-fields');
    const previewContainer = document.getElementById('preview-container');
    const previewContent = document.getElementById('preview-content');
    const form = document.querySelector('form');
    const contentInput = document.getElementById('content');
    const headingInput = document.querySelector('#layout1-fields input[name="heading"]');
    const subheadingInput = document.querySelector('#layout1-fields input[name="subheading"]');
    const feature1Input = document.querySelector('#layout1-fields input[name="feature1"]');
    const feature2Input = document.querySelector('#layout1-fields input[name="feature2"]');
    const feature3Input = document.querySelector('#layout1-fields input[name="feature3"]');
    const buttonTextInput = document.querySelector('#layout1-fields input[name="button_text"]');
    const offerTextInput = document.querySelector('#layout1-fields input[name="offer_text"]');
    const offerSubtextInput = document.querySelector('#layout1-fields input[name="offer_subtext"]');
    const fileInput = document.getElementById('image');

    const heading2Input = document.querySelector('#layout2-fields input[name="heading"]');
    const location2Input = document.querySelector('#layout2-fields input[name="location"]');
    const availability2Input = document.querySelector('#layout2-fields input[name="availability"]');
    const buttonText2Input = document.querySelector('#layout2-fields input[name="button_text"]');
    const description2Input = document.querySelector('#layout2-fields input[name="description"]');

     function updatePreview() {
        const selectedLayout = layoutSelect.value;
        let previewHTML = '';

        if (selectedLayout === 'layout1') {
            previewHTML = `
                <div class="max-w-md mx-auto overflow-hidden rounded-xl shadow-lg bg-white">
                    <div class="relative p-8 text-white" style="background-color: ${document.getElementById('background_color').value}">
                        <div class="absolute -right-4 -top-4 h-24 w-24 rounded-full bg-yellow-400 opacity-30"></div>
                        <h2 class="text-3xl font-bold mb-2">${headingInput.value || 'Paradise Cove Resort'}</h2>
                        <p class="text-lg mb-6">${subheadingInput.value || 'Your dream vacation awaits'}</p>
                        <div class="space-y-3 mb-8">
                            <div class="flex items-center">
                                <div class="w-6 h-6 rounded-full bg-white flex items-center justify-center mr-3">
                                    <span class="text-teal-600 text-xs font-bold">✓</span>
                                </div>
                                <p>${feature1Input.value || 'Luxury beachfront accommodations'}</p>
                            </div>
                            <div class="flex items-center">
                                <div class="w-6 h-6 rounded-full bg-white flex items-center justify-center mr-3">
                                    <span class="text-teal-600 text-xs font-bold">✓</span>
                                </div>
                                <p>${feature2Input.value || 'World-class dining experiences'}</p>
                            </div>
                            <div class="flex items-center">
                                <div class="w-6 h-6 rounded-full bg-white flex items-center justify-center mr-3">
                                    <span class="text-teal-600 text-xs font-bold">✓</span>
                                </div>
                                <p>${feature3Input.value || 'Exclusive spa and wellness center'}</p>
                            </div>
                        </div>
                         <button class="bg-white text-teal-600 hover:bg-teal-50 px-4 py-2 rounded-md font-medium">
                            ${buttonTextInput.value || 'Book Now'}
                        </button>

                        <div class="absolute -bottom-10 left-0 right-0 h-20 overflow-hidden">
                            <svg viewBox="0 0 500 150" preserveAspectRatio="none" class="h-full w-full">
                                <path d="M0.00,49.98 C150.00,150.00 349.20,-50.00 500.00,49.98 L500.00,150.00 L0.00,150.00 Z" class="fill-current" style="fill: ${document.getElementById('background_color').value}"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="relative h-64 w-full">
                      <img id="preview-image"
                            src="${fileInput.files[0] ? URL.createObjectURL(fileInput.files[0]) : 'https://images.unsplash.com/photo-1540541338287-41700207dee6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80'}"
                           alt="Paradise Cove Resort beach view"
                           class="object-cover w-full h-full"
                         />
                        <div class="absolute bottom-4 right-4 bg-white bg-opacity-90 p-3 rounded-lg shadow-md">
                            <p class="text-teal-600 font-bold">${offerTextInput.value || 'Limited Time Offer'}</p>
                            <p class="text-gray-700 text-sm">${offerSubtextInput.value || '30% off summer packages'}</p>
                        </div>
                    </div>
                </div>
            `;
        } else if (selectedLayout === 'layout2') {
            previewHTML = `
               <div class="max-w-md w-full overflow-hidden rounded-xl shadow-xl">
                    <div class="p-8 text-white relative" style="background-color: ${document.getElementById('background_color').value}">
                        <div class="flex items-center gap-2 mb-2">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                             <h3 class="text-sm font-medium uppercase tracking-wider">Luxury Getaway</h3>
                        </div>
                         <h2 class="text-3xl font-bold mb-4">${heading2Input.value || 'Paradise Beach Resort'}</h2>
                         <p class="mb-6 opacity-90">
                           ${description2Input.value || 'Experience the ultimate relaxation in our beachfront villas with stunning ocean views and world-class amenities.'}
                        </p>
                        <div class="flex items-center gap-2 mb-3">
                           <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>
                            <span class="text-sm">${location2Input.value || 'Maldives, Indian Ocean'}</span>
                        </div>
                        <div class="flex items-center gap-2 mb-6">
                           <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>
                             <span class="text-sm">${availability2Input.value || 'Available year-round'}</span>
                         </div>
                         <button class="w-full bg-white text-black hover:bg-white/90">${buttonText2Input.value || 'Book Your Stay'}</button>
                    </div>
                     <div class="relative h-64 bg-white">
                        <div class="absolute top-0 left-0 right-0 h-16 z-10" style="background-color: ${document.getElementById('background_color').value}; border-bottom-left-radius: 50% 100%; border-bottom-right-radius: 50% 100%;"></div>
                         <div class="absolute inset-0 mt-6">
                           <img id="preview-image"
                                 src="${fileInput.files[0] ? URL.createObjectURL(fileInput.files[0]) : 'https://images.unsplash.com/photo-1499793983690-e29da59ef1c2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'}"
                               alt="Beautiful beach resort view"
                                 class="object-cover w-full h-full"
                            />
                        </div>
                     </div>
               </div>
            `;
        }

        previewContent.innerHTML = previewHTML;
         contentInput.value = previewHTML; // VERY VERY IMPORTANT

    }

    fileInput.addEventListener('change', function() {
        updatePreview();
        const previewImage = document.getElementById('preview-image');
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
            };
            reader.readAsDataURL(this.files[0]);
        }
    });

    layoutSelect.addEventListener('change', function() {
        if (this.value === 'layout1') {
            layout1Fields.style.display = 'block';
            layout2Fields.style.display = 'none';
        } else if (this.value === 'layout2') {
            layout2Fields.style.display = 'block';
            layout1Fields.style.display = 'none';
        }
        updatePreview();
    });

    document.getElementById('background_color').addEventListener('input', updatePreview);

    if (headingInput) headingInput.addEventListener('input', updatePreview);
    if (subheadingInput) subheadingInput.addEventListener('input', updatePreview);
    if (feature1Input) feature1Input.addEventListener('input', updatePreview);
    if (feature2Input) feature2Input.addEventListener('input', updatePreview);
    if (feature3Input) feature3Input.addEventListener('input', updatePreview);
    if (buttonTextInput) buttonTextInput.addEventListener('input', updatePreview);
    if (offerTextInput) offerTextInput.addEventListener('input', updatePreview);
    if (offerSubtextInput) offerSubtextInput.addEventListener('input', updatePreview);

    if (heading2Input) heading2Input.addEventListener('input', updatePreview);
    if (location2Input) location2Input.addEventListener('input', updatePreview);
    if (availability2Input) availability2Input.addEventListener('input', updatePreview);
    if (buttonText2Input) buttonText2Input.addEventListener('input', updatePreview);
    if (description2Input) description2Input.addEventListener('input', updatePreview);

    updatePreview(); // Initial preview

// NO NEED FOR THIS ANYMORE
   //  form.addEventListener('submit', function(event) {
   //    event.preventDefault(); // Prevent default submission

   //     // **Crucial: Update the hidden input *immediately* before submission**
   //     contentInput.value = previewContent.innerHTML;
   //    this.submit();

   // });
});
</script>
</x-app-layout>