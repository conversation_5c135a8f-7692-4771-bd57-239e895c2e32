<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ $template->name }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                <div class="flex justify-between mb-4">
                    <h3 class="text-lg font-medium">Template Details</h3>
                    <div>
                        <a href="{{ route('templates.edit', $template) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2">Edit</a>
                        <form action="{{ route('templates.destroy', $template) }}" method="POST" class="inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded" onclick="return confirm('Are you sure you want to delete this template?')">Delete</button>
                        </form>
                    </div>
                </div>

                <div class="mb-6">
                    <h4 class="font-medium mb-2">Template Preview:</h4>
                    <div class="border rounded-lg p-4">
                        <!-- Render the actual HTML content -->
                        <div class="max-w-md mx-auto">
                            {!! $template->content !!}
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <h4 class="font-medium mb-2">Template URL:</h4>
                    <div class="border rounded-lg p-4 bg-gray-50">
                        <code class="text-sm break-all">{{ $template->getContentUrl() }}</code>
                    </div>
                </div>

                <div class="mt-6">
                    <a href="{{ route('templates.index') }}" class="text-blue-500 hover:underline">Back to Templates</a>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>