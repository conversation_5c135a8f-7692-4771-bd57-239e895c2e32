<x-app-layout>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        .card {
            background: white;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }
        
        .card-hover:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .input-field {
            border: 1px solid #e2e8f0;
            background: white;
            transition: all 0.2s ease;
        }
        
        .input-field:focus {
            border-color: #0ea5e9;
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
            outline: none;
        }
        
        .btn-primary {
            background: #0ea5e9;
            transition: all 0.2s ease;
        }
        
        .btn-primary:hover {
            background: #0284c7;
            transform: translateY(-1px);
        }
        
        .animate-fade-in {
            animation: fadeIn 0.6s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Management Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Custom Fields Section -->
            <div class="card rounded-xl p-6 animate-fade-in">
                <div class="flex items-center mb-6">
                    <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Custom Fields</h3>
                </div>
                
                <form method="POST" action="{{ route('fields-and-tags.storeField') }}" class="space-y-4 mb-6">
                    @csrf
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Field Title</label>
                        <input type="text" name="title" placeholder="Enter field name" 
                            class="input-field w-full px-3 py-2 rounded-lg" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Field Type</label>
                        <select name="type" id="fieldType" required 
                            class="input-field w-full px-3 py-2 rounded-lg"
                            onchange="toggleOptionsField()">
                            <option value="text">Text</option>
                            <option value="number">Number</option>
                            <option value="date">Date</option>
                            <option value="radio">Radio Buttons</option>
                            <option value="checkbox">Checkboxes</option>
                        </select>
                    </div>
                    
                    <div id="optionsField" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Options</label>
                        <input type="text" name="options" placeholder="Option 1, Option 2, Option 3" 
                            class="input-field w-full px-3 py-2 rounded-lg">
                        <p class="text-xs text-gray-500 mt-1">Separate options with commas</p>
                    </div>
                    
                    <button type="submit" class="btn-primary w-full text-white px-4 py-2 rounded-lg font-medium">
                        Add Field
                    </button>
                </form>
                
                <div class="overflow-hidden rounded-lg border border-gray-200">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Field</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Options</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach ($customFields as $field)
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">{{ $field->title }}</td>
                                <td class="px-4 py-3 text-sm text-gray-600">
                                    <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
                                        {{ ucfirst($field->type) }}
                                    </span>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-600">{{ $field->options }}</td>
                                <td class="px-4 py-3 text-sm">
                                <button onclick="openEditFieldModal({{ $field->id }}, '{{ addslashes($field->title) }}', '{{ $field->type }}', '{{ addslashes($field->options) }}')" class="text-gray-400 hover:text-blue-600 mr-2 transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 11l6 6m-6-6l-3 3m3-3l3-3m-3 3v6"></path>
                                    </svg>
                                </button>
                                <form method="POST" action="{{ route('fields-and-tags.destroyField', $field) }}" 
                                    onsubmit="return confirm('Delete this field?');" class="inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-gray-400 hover:text-red-600 transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </form>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Tags Section -->
            <div class="card rounded-xl p-6 animate-fade-in">
                <div class="flex items-center mb-6">
                    <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Tags</h3>
                </div>
                
                <form method="POST" action="{{ route('fields-and-tags.storeTag') }}" class="space-y-4 mb-6">
                    @csrf
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tag Name</label>
                        <input type="text" name="name" placeholder="Enter tag name" required
                            class="input-field w-full px-3 py-2 rounded-lg">
                    </div>
                    
                    <button type="submit" class="btn-primary w-full text-white px-4 py-2 rounded-lg font-medium">
                        Add Tag
                    </button>
                </form>
                
                <div class="overflow-hidden rounded-lg border border-gray-200">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Tag Name</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach ($tags as $tag)
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">
                                    <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-800">
                                        {{ $tag->name }}
                                    </span>
                                </td>
                                <td class="px-4 py-3 text-sm">
                                <button onclick="openEditTagModal({{ $tag->id }}, '{{ addslashes($tag->name) }}')" class="text-gray-400 hover:text-blue-600 mr-2 transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 11l6 6m-6-6l-3 3m3-3l3-3m-3 3v6"></path>
                                    </svg>
                                </button>
                                <form method="POST" action="{{ route('fields-and-tags.destroyTag', $tag) }}" 
                                    onsubmit="return confirm('Delete this tag?');" class="inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-gray-400 hover:text-red-600 transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </form>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Enhanced Preview Section -->
        <div class="card rounded-xl p-6 animate-fade-in">
            <div class="flex items-center mb-6">
                <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Form Preview</h3>
                    <p class="text-gray-600 text-sm mt-1">Preview how your customer form will appear</p>
                </div>
            </div>

            <div class="bg-white rounded-xl border border-gray-200 p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Basic Information -->
                    <div class="space-y-5">
                        <div class="pb-3 border-b border-gray-200">
                            <h4 class="text-base font-medium text-gray-900">Basic Information</h4>
                        </div>
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                                <input type="text" class="input-field w-full px-3 py-2 rounded-lg" disabled>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                                <input type="tel" class="input-field w-full px-3 py-2 rounded-lg" disabled>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                                <input type="email" class="input-field w-full px-3 py-2 rounded-lg" disabled>
                            </div>
                        </div>
                    </div>

                    <!-- Custom Fields -->
                    <div class="space-y-5">
                        <div class="pb-3 border-b border-gray-200">
                            <h4 class="text-base font-medium text-gray-900">Additional Information</h4>
                        </div>
                        
                        <div class="space-y-4">
                            @foreach ($customFields as $field)
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ $field->title }}</label>
                                @if ($field->type === 'text')
                                    <input type="text" class="input-field w-full px-3 py-2 rounded-lg" disabled>
                                @elseif ($field->type === 'number')
                                    <input type="number" class="input-field w-full px-3 py-2 rounded-lg" disabled>
                                @elseif ($field->type === 'date')
                                    <input type="date" class="input-field w-full px-3 py-2 rounded-lg" disabled>
                                @elseif ($field->type === 'radio')
                                    <div class="space-y-2">
                                        @foreach (explode(',', $field->options) as $option)
                                            <label class="flex items-center">
                                                <input type="radio" class="text-blue-600 focus:ring-blue-500" disabled>
                                                <span class="ml-2 text-sm text-gray-700">{{ trim($option) }}</span>
                                            </label>
                                        @endforeach
                                    </div>
                                @elseif ($field->type === 'checkbox')
                                    <div class="space-y-2">
                                        @foreach (explode(',', $field->options) as $option)
                                            <label class="flex items-center">
                                                <input type="checkbox" class="text-blue-600 focus:ring-blue-500" disabled>
                                                <span class="ml-2 text-sm text-gray-700">{{ trim($option) }}</span>
                                            </label>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Tags Section -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <h4 class="text-base font-medium text-gray-900 mb-4">Tags</h4>
                    <div class="flex flex-wrap gap-2">
                        @foreach ($tags as $tag)
                            <span class="bg-gray-100 text-gray-800 px-3 py-1 rounded-md text-sm font-medium">
                                {{ $tag->name }}
                            </span>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Field Modal -->
    <div id="editFieldModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 hidden" x-data>
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl shadow-lg max-w-lg w-full p-6">
                <h3 class="text-lg font-semibold mb-4">Edit Field</h3>
                <form id="editFieldForm" method="POST" class="space-y-4">
                    @csrf
                    @method('PUT')
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Field Title</label>
                        <input type="text" name="title" id="editFieldTitle" required 
                            class="input-field w-full px-3 py-2 rounded-lg">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Field Type</label>
                        <select name="type" id="editFieldType" required 
                            class="input-field w-full px-3 py-2 rounded-lg"
                            onchange="toggleEditOptionsField()">
                            <option value="text">Text</option>
                            <option value="number">Number</option>
                            <option value="date">Date</option>
                            <option value="radio">Radio Buttons</option>
                            <option value="checkbox">Checkboxes</option>
                        </select>
                    </div>
                    
                    <div id="editOptionsField" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Options</label>
                        <textarea name="options" id="editFieldOptions" rows="3" 
                            class="input-field w-full px-3 py-2 rounded-lg"
                            placeholder="Enter each option on a new line"></textarea>
                        <p class="text-xs text-gray-500 mt-1">Press Enter to add new options</p>
                    </div>

                    <div class="flex justify-between space-x-3 mt-6">
                        <button type="button" onclick="closeEditFieldModal()" 
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" 
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Tag Modal -->
    <div id="editTagModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl shadow-lg max-w-lg w-full p-6">
                <h3 class="text-lg font-semibold mb-4">Edit Tag</h3>
                <form id="editTagForm" method="POST" class="space-y-4">
                    @csrf
                    @method('PUT')
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tag Name</label>
                        <input type="text" name="name" id="editTagName" required 
                            class="input-field w-full px-3 py-2 rounded-lg">
                    </div>

                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" onclick="closeEditTagModal()" 
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" 
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function toggleOptionsField() {
            const fieldType = document.getElementById('fieldType').value;
            const optionsField = document.getElementById('optionsField');
            if (fieldType === 'radio' || fieldType === 'checkbox') {
                optionsField.classList.remove('hidden');
            } else {
                optionsField.classList.add('hidden');
            }
        }

        function toggleEditOptionsField() {
            const fieldType = document.getElementById('editFieldType').value;
            const optionsField = document.getElementById('editOptionsField');
            if (fieldType === 'radio' || fieldType === 'checkbox') {
                optionsField.classList.remove('hidden');
            } else {
                optionsField.classList.add('hidden');
            }
        }

        function openEditFieldModal(id, title, type, options) {
            const modal = document.getElementById('editFieldModal');
            const form = document.getElementById('editFieldForm');
            const titleInput = document.getElementById('editFieldTitle');
            const typeSelect = document.getElementById('editFieldType');
            const optionsInput = document.getElementById('editFieldOptions');

            form.action = `/fields-and-tags/field/${id}`;
            titleInput.value = title;
            typeSelect.value = type;
            optionsInput.value = options ? options.split(',').map(o => o.trim()).join('\n') : '';

            toggleEditOptionsField();
            modal.classList.remove('hidden');
        }

        function closeEditFieldModal() {
            document.getElementById('editFieldModal').classList.add('hidden');
        }

        function openEditTagModal(id, name) {
            const modal = document.getElementById('editTagModal');
            const form = document.getElementById('editTagForm');
            const nameInput = document.getElementById('editTagName');

            form.action = `/fields-and-tags/tag/${id}`;
            nameInput.value = name;
            modal.classList.remove('hidden');
        }

        function closeEditTagModal() {
            document.getElementById('editTagModal').classList.add('hidden');
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Animation delay for cards
            const cards = document.querySelectorAll('.animate-fade-in');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });

            // Prevent form submission on Enter for options textarea
            const optionsTextarea = document.getElementById('editFieldOptions');
            if (optionsTextarea) {
                optionsTextarea.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        // Add a new line instead of submitting
                        const start = this.selectionStart;
                        const end = this.selectionEnd;
                        this.value = this.value.substring(0, start) + "\n" + this.value.substring(end);
                        this.selectionStart = this.selectionEnd = start + 1;
                    }
                });
            }

            // Close modals when clicking outside
            window.addEventListener('click', function(e) {
                const editFieldModal = document.getElementById('editFieldModal');
                const editTagModal = document.getElementById('editTagModal');
                
                if (e.target === editFieldModal) {
                    closeEditFieldModal();
                }
                if (e.target === editTagModal) {
                    closeEditTagModal();
                }
            });
        });
    </script>
</x-app-layout>
