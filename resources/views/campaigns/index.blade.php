<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <h2 class="text-2xl font-semibold text-gray-800">
                Campaign Creator
            </h2>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                Draft
            </span>
        </div>
    </x-slot>

    <div class="flex gap-6">
        <!-- Left Column - Customer Selection -->
        <div class="w-1/2 bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Customer Targeting</h3>
                
                <!-- Filter Grid -->
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <!-- Tag Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Customer Tags</label>
                        <select id="tag_filter" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white text-gray-900">
                            <option value="all">All Tags</option>
                            @foreach($tags as $tag)
                                <option value="{{ $tag->id }}">{{ $tag->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Date Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Join Date</label>
                        <select id="date_filter" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white text-gray-900">
                            <option value="">Any Time</option>
                            <option value="new">Newest First</option>
                            <option value="old">Oldest First</option>
                        </select>
                    </div>

                    <!-- Spending Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Spending Level</label>
                        <select id="spending_filter" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white text-gray-900">
                            <option value="">All Spending</option>
                            <option value="desc">High Spenders</option>
                            <option value="asc">Low Spenders</option>
                        </select>
                    </div>

                    <!-- Activity Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Activity Status</label>
                        <select id="activity_filter" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white text-gray-900">
                            <option value="">All Activity</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>

                <!-- Apply Filters Button -->
                <button type="button" id="filter_customer" class="w-full inline-flex items-center justify-center px-4 py-2.5 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-colors duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                    </svg>
                    Apply Filters
                </button>
            </div>

            <!-- Customer List -->
            <div class="mb-4">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-sm font-medium text-gray-700">Selected Customers</h4>
                    <span id="selected_count" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        0 Selected
                    </span>
                </div>
                
                <div class="border border-gray-200 rounded-lg overflow-hidden">
                    <div class="bg-gray-50 px-4 py-2 border-b border-gray-200">
                        <label class="inline-flex items-center">
                            <input type="checkbox" id="select_all" class="rounded border-gray-300 text-blue-600 shadow-sm focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Select All</span>
                        </label>
                    </div>
                    <ul id="customer_list" class="divide-y divide-gray-200 max-h-96 overflow-y-auto">
                        @foreach($customers as $customer)
                        <li class="hover:bg-gray-50 transition-colors duration-150">
                            <label class="flex items-center px-4 py-3 cursor-pointer">
                                <input type="checkbox" name="customer_ids[]" value="{{ $customer->id }}" id="customer_{{ $customer->id }}" 
                                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:ring-blue-500">
                                <div class="ml-3">
                                    <span class="block text-sm font-medium text-gray-900">{{ $customer->name }}</span>
                                    <span class="block text-sm text-gray-500">{{ $customer->phone_number }}</span>
                                </div>
                            </label>
                        </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>

        <!-- Right Column - Message Composition -->
        <!-- Right Column - Message Composition -->
<div class="w-1/2 bg-white rounded-xl shadow-lg p-6 border border-gray-100">
    <h3 class="text-lg font-semibold text-gray-800 mb-6">Campaign Message</h3>
    <form id="campaignForm" method="POST" action="{{ route('campaigns.send') }}" enctype="multipart/form-data">
        @csrf

        <!-- Template Selection -->
        <div class="mb-6">
            <label for="template_id" class="block text-sm font-medium text-gray-700 mb-2">Select Template (Optional):</label>
            <select name="template_id" id="template_id" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white text-gray-900">
                <option value="">No Template</option>
                @foreach($templates as $template)
                    <option value="{{ $template->id }}">{{ $template->name }}</option>
                @endforeach
            </select>
        </div>


        <!-- Message Input (Conditional) -->
        <div class="mb-6" id="message-input-container">
            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message Content (Optional):</label>
            <textarea
                name="message"
                id="message"
                rows="6"
                class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                placeholder="Write your campaign message here..."
            ></textarea>
        </div>

        <!-- Media Upload -->
        <div class="mb-6">
            <label for="media" class="block text-sm font-medium text-gray-700 mb-2">Attach Media (Optional):</label>
            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 transition-colors duration-200">
                <div class="space-y-1 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <div class="flex text-sm text-gray-600">
                        <label for="media" class="relative cursor-pointer rounded-md font-medium text-blue-600 hover:text-blue-500">
                            <span>Upload a file</span>
                            <input id="media" name="media" type="file" class="sr-only">
                        </label>
                        <p class="pl-1">or drag and drop</p>
                    </div>
                    <p class="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                </div>
            </div>
        </div>

        <input type="hidden" name="customer_ids" id="selected_customer_ids">

        <!-- Submit Button -->
        <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2.5 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-colors duration-200">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
            Send Campaign
        </button>
    </form>
</div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const filterCustomerButton = document.getElementById('filter_customer');
            const customerList = document.getElementById('customer_list');
            const tagFilter = document.getElementById('tag_filter');
            const dateFilter = document.getElementById('date_filter');
            const spendingFilter = document.getElementById('spending_filter');
            const activityFilter = document.getElementById('activity_filter');
            const campaignForm = document.getElementById('campaignForm');
            const selectedCustomerIdsInput = document.getElementById('selected_customer_ids');
            const selectAllCheckbox = document.getElementById('select_all');
            const selectedCountSpan = document.getElementById('selected_count');
            let selectedCustomerIds = [];

             const templateSelect = document.getElementById('template_id');
            const messageInputContainer = document.getElementById('message-input-container');

            // Function to toggle message input based on template selection
            function toggleMessageInput() {
                if (templateSelect.value !== '') {
                    messageInputContainer.style.display = 'none';
                } else {
                    messageInputContainer.style.display = 'block';
                }
            }

            // Event listener for template selection change
           templateSelect.addEventListener('change', toggleMessageInput);


            // Update the selected customer count
            function updateSelectedCount() {
                selectedCountSpan.textContent = `${selectedCustomerIds.length} Selected`;
            }

            // Update the selected customer IDs array
            function updateSelectedCustomers(checkbox) {
                if (checkbox.checked) {
                    selectedCustomerIds.push(parseInt(checkbox.value));
                } else {
                    selectedCustomerIds = selectedCustomerIds.filter(id => id !== parseInt(checkbox.value));
                }
                selectedCustomerIdsInput.value = JSON.stringify(selectedCustomerIds);
                updateSelectedCount();
            }

            // Handle "Select All" functionality
            selectAllCheckbox.addEventListener('change', function() {
                const checkboxes = customerList.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                    updateSelectedCustomers(checkbox);
                });
            });

            // Handle individual checkbox changes
            customerList.addEventListener('change', function(event) {
                if (event.target.type === 'checkbox') {
                    updateSelectedCustomers(event.target);
                }
            });

            // Apply filters and fetch customers
            filterCustomerButton.addEventListener('click', async function() {
                const tagId = tagFilter.value;
                const dateFilterValue = dateFilter.value;
                const spendingFilterValue = spendingFilter.value;
                const activityFilterValue = activityFilter.value;

                try {
                    const response = await fetch('{{ route('campaigns.get.filtered.customers') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            tag_id: tagId,
                            date_filter: dateFilterValue,
                            spending_filter: spendingFilterValue,
                            activity_filter: activityFilterValue,
                        })
                    });

                    const customers = await response.json();
                    customerList.innerHTML = '';

                    customers.forEach(customer => {
                        const li = document.createElement('li');
                        li.classList.add('hover:bg-gray-50', 'transition-colors', 'duration-150');
                        li.innerHTML = `
                            <label class="flex items-center px-4 py-3 cursor-pointer">
                                <input type="checkbox" name="customer_ids[]" value="${customer.id}" id="customer_${customer.id}" 
                                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:ring-blue-500">
                                <div class="ml-3">
                                    <span class="block text-sm font-medium text-gray-900">${customer.name}</span>
                                    <span class="block text-sm text-gray-500">${customer.phone_number}</span>
                                </div>
                            </label>
                        `;
                        customerList.appendChild(li);

                        // Restore selected state for checkboxes
                        const checkbox = li.querySelector('input[type="checkbox"]');
                        if (selectedCustomerIds.includes(customer.id)) {
                            checkbox.checked = true;
                        }
                        checkbox.addEventListener('change', function() {
                            updateSelectedCustomers(this);
                        });
                    });
                } catch (error) {
                    console.error('Error fetching customers:', error);
                }
            });


            // Handle form submission
            campaignForm.addEventListener('submit', function(event) {
              // Check if either a template is selected or a message is entered
             if (!templateSelect.value && !document.getElementById('message').value) {
                  event.preventDefault();  // Prevent form submission
                 alert('Please select a template or enter a message.');
                 return; // Stop the function execution
             }

              if (selectedCustomerIds.length === 0) {
                    event.preventDefault();
                    alert('Please select at least one customer to send the campaign.');
               }
            });

             // Initial check on page load
             toggleMessageInput();
        });
    </script>
</x-app-layout>