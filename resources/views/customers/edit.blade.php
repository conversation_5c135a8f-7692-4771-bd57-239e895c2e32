<x-app-layout>
      <x-slot name="header">
          <h2 class="font-semibold text-2xl text-gray-800 leading-tight">
             Edit Customer
          </h2>
      </x-slot>

      <div class="py-8">
         <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div class="bg-white rounded-xl shadow-lg p-6">
                  <form action="{{ route('customers.update', $customer) }}" method="POST">
                      @csrf
                      @method('PUT')
                      <div class="space-y-6">
                          <!-- Name -->
                           <div class="mb-6">
                              <label class="block text-sm font-medium text-gray-700 mb-2">Name</label>
                              <input type="text" name="name" value="{{ $customer->name }}" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" required>
                          </div>

                          <!-- Phone Number -->
                          <div class="mb-6">
                              <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                              <input type="text" name="phone_number" value="{{ $customer->phone_number }}" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" required>
                         </div>

                          <!-- Email -->
                          <div class="mb-6">
                             <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                              <input type="email" name="email" value="{{ $customer->email }}" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
                         </div>
                         <div class="mb-6">
                              <label class="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
                              <input type="date" name="date_of_birth" value="{{ $customer->date_of_birth }}" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
                         </div>
                         <div class="mb-6">
                              <label class="block text-sm font-medium text-gray-700 mb-2">Anniversary Date</label>
                             <input type="date" name="anniversary_date" value="{{ $customer->anniversary_date }}" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
                         </div>


                          <!-- Tags -->
                         <div class="mb-6">
                              <label class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                              <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                                  @foreach($tags as $tag)
                                      <label class="inline-flex items-center">
                                        <input type="checkbox" name="tags[]" value="{{ $tag->id }}"
                                              class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                              @if($customer->tags->contains($tag)) checked @endif>
                                           <span class="ml-2 text-sm text-gray-700">{{ $tag->name }}</span>
                                      </label>
                                 @endforeach
                              </div>
                         </div>

                         <!-- Custom Fields -->
                          <div class="mb-6">
                             <label class="block text-sm font-medium text-gray-700 mb-2">Custom Fields</label>
                              @foreach($customFields as $field)
                                  <div class="mb-4">
                                     <label class="block text-sm font-medium text-gray-700 mb-1">{{ $field->title }}</label>
                                       @php
                                         $fieldValue = $customer->customFieldValues->where('custom_field_id', $field->id)->first()?->value;
                                       @endphp
                                      @if($field->type == 'text')
                                         <input type="text" name="custom_fields[{{ $field->id }}]" value="{{ $fieldValue }}" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
                                      @elseif($field->type == 'date')
                                          <input type="date" name="custom_fields[{{ $field->id }}]" value="{{ $fieldValue }}" class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
                                      @elseif($field->type == 'radio' && $field->options)
                                         @foreach($field->options as $option)
                                              <label class="inline-flex items-center mr-4">
                                                  <input type="radio" name="custom_fields[{{ $field->id }}]" value="{{ $option }}"
                                                       class="rounded-full border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                                      @if($fieldValue == $option) checked @endif>
                                                  <span class="ml-2 text-sm text-gray-700">{{ $option }}</span>
                                            </label>
                                         @endforeach
                                     @elseif($field->type == 'checkbox' && $field->options)
                                         @foreach($field->options as $option)
                                              <label class="inline-flex items-center mr-4">
                                                  <input type="checkbox" name="custom_fields[{{ $field->id }}][]" value="{{ $option }}"
                                                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                                      @if(is_array($fieldValue) && in_array($option, $fieldValue)) checked @endif>
                                                  <span class="ml-2 text-sm text-gray-700">{{ $option }}</span>
                                              </label>
                                         @endforeach
                                      @endif
                                 </div>
                               @endforeach
                         </div>

                          <!-- Submit Button -->
                          <div class="flex justify-end">
                              <button type="submit" class="inline-flex items-center px-4 py-2.5 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-colors duration-200">
                                  Update Customer
                              </button>
                          </div>
                      </div>
                  </form>
              </div>
          </div>
      </div>
  </x-app-layout>