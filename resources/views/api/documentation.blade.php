<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-2xl text-gray-800 leading-tight">
            API Documentation
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold mb-4">Your API Key</h3>
                        <div class="bg-gray-100 p-4 rounded-lg flex items-center justify-between">
                            <div class="flex items-center space-x-2 flex-1">
                                <code class="text-sm" id="apiKey" style="filter: blur(4px);">{{ $user->api_key }}</code>
                                <button onclick="toggleApiKey()" class="p-2 text-gray-500 hover:text-gray-700">
                                    <svg id="eyeIcon" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                </button>
                            </div>
                            <button onclick="copyApiKey()" class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm">
                                Copy
                            </button>
                        </div>
                    </div>

                    <div class="mb-8">
                        <h3 class="text-lg font-semibold mb-4">Enquiry API Endpoint</h3>
                        <p class="mb-4">Submit enquiry data to the system via:</p>
                        <pre class="bg-gray-100 p-4 rounded text-sm overflow-x-auto">POST {{ url('/api/enquiries') }}</pre>
                    </div>

                    <div class="mb-8">
                        <h3 class="text-lg font-semibold mb-4">Authentication</h3>
                        <p class="mb-4">Include your API key in the request header:</p>
                        <pre class="bg-gray-100 p-4 rounded text-sm overflow-x-auto">X-API-KEY: your_api_key_here</pre>
                    </div>

                    <div class="mb-8">
                        <h3 class="text-lg font-semibold mb-4">Request Parameters</h3>
                        <p class="mb-4">Send a JSON body with the following parameters:</p>
                        <div class="bg-gray-100 p-4 rounded">
                            <ul class="list-disc list-inside space-y-2">
                                <li><code>name</code> (string, required): Customer name</li>
                                <li><code>phone_number</code> (string, required): Customer phone number</li>
                                <li><code>lead_type</code> (string, required): Lead type, one of: hot, mild, cold</li>
                                <li><code>additional_notes</code> (string, optional): Additional notes</li>
                                <li><code>responses</code> (array, optional): Array of question responses, each containing:</li>
                                <ul class="list-disc list-inside ml-6">
                                    <li><code>question_id</code> (integer, required): Enquiry question ID</li>
                                    <li><code>response</code> (string, required): Response text</li>
                                </ul>
                            </ul>
                        </div>
                    </div>

                    <div class="mb-8">
                        <h3 class="text-lg font-semibold mb-4">Example Request</h3>
                        <pre class="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
curl -X POST {{ url('/api/enquiries') }} \\
-H "X-API-KEY: your_api_key_here" \\
-H "Content-Type: application/json" \\
-d '{
    "name": "John Doe",
    "phone_number": "9876543210",
    "lead_type": "hot",
    "additional_notes": "Interested in product X",
    "responses": [
        {"question_id": 1, "response": "Yes"},
        {"question_id": 2, "response": "No"}
    ]
}'</pre>
                    </div>

                    <div>
                        <h3 class="text-lg font-semibold mb-4">Response Format</h3>
                        <pre class="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
{
    "message": "Enquiry created successfully",
    "enquiry": {
        "id": 1,
        "name": "John Doe",
        "phone_number": "9876543210",
        "lead_type": "hot",
        "additional_notes": "Interested in product X",
        "created_at": "2024-03-20T12:00:00.000000Z",
        "updated_at": "2024-03-20T12:00:00.000000Z",
        "responses": [
            {
                "id": 1,
                "enquiry_id": 1,
                "question_id": 1,
                "response": "Yes"
            },
            {
                "id": 2,
                "enquiry_id": 1,
                "question_id": 2,
                "response": "No"
            }
        ]
    }
}</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function copyApiKey() {
            const apiKey = document.getElementById('apiKey').textContent;
            navigator.clipboard.writeText(apiKey).then(() => {
                alert('API key copied to clipboard!');
            });
        }

        function toggleApiKey() {
            const apiKeyElement = document.getElementById('apiKey');
            const eyeIcon = document.getElementById('eyeIcon');
            if (apiKeyElement.style.filter === 'none') {
                apiKeyElement.style.filter = 'blur(4px)';
                eyeIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />';
            } else {
                apiKeyElement.style.filter = 'none';
                eyeIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.542-7a9.956 9.956 0 012.223-3.182m3.1-2.724A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.542 7a10.06 10.06 0 01-4.132 5.411M15 12a3 3 0 11-6 0 3 3 0 016 0z" />';
            }
        }
    </script>
</x-app-layout>
