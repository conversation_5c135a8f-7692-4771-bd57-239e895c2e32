<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-100">
             <!-- Top Navigation Bar -->
            <nav class="bg-white border-b border-gray-200 fixed right-0 left-0 z-10">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <!-- Logo -->
                            <div class="shrink-0 flex items-center">
                                <a href="{{ route('marketing.dashboard') }}">
                                    <img src="{{ asset('images/logo.jpg') }}" alt="{{ config('app.name', 'Laravel') }}" class="h-9 w-auto">
                                </a>
                           </div>
                        </div>

                        <!-- Profile Dropdown -->
                        <div class="hidden sm:flex sm:items-center sm:ml-6">
                            <div class="relative">
                                <button id="profile-dropdown-button" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                   {{ Auth::guard('marketing')->user()->name }}
                                   <svg class="ml-2 -mr-1 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                     </svg>
                                </button>

                                <!-- Dropdown Menu -->
                                <div id="profile-dropdown-menu" class="origin-top-right absolute right-0 mt-2 w-56 rounded-lg shadow-lg bg-white ring-1 ring-black ring-opacity-5 divide-y divide-gray-100 hidden">
                                    <div class="py-1">
                                         <form method="POST" action="{{ route('marketing.logout') }}">
                                             @csrf
                                             <button type="submit" class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left">
                                                 {{ __('Log Out') }}
                                             </button>
                                         </form>
                                     </div>
                                 </div>
                             </div>
                         </div>
                    </div>
                </div>
             </nav>
               <!-- Page Content -->
            <div class="pt-16"> <!-- Add padding-top to account for fixed navbar -->
                <!-- Page Heading -->
                @if (isset($header))
                    <header class="bg-white shadow">
                        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                            {{ $header }}
                        </div>
                    </header>
                @endif
                 <!-- Main Content -->
                <main class="p-6">
                    @if (session('success'))
                        <div class="rounded-lg bg-green-50 p-4 border border-green-200 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                     <svg class="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                      </svg>
                                 </div>
                                 <div class="ml-3">
                                    <h3 class="text-sm font-medium text-green-800">Success</h3>
                                     <div class="mt-2 text-sm text-green-700">
                                         <p>{{ session('success') }}</p>
                                     </div>
                                 </div>
                             </div>
                        </div>
                    @endif

                    @if ($errors->any())
                        <div class="rounded-lg bg-red-50 p-4 border border-red-200 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                     <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                         <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                      </svg>
                                 </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">Error</h3>
                                     <div class="mt-2 text-sm text-red-700">
                                         <ul>
                                             @foreach ($errors->all() as $error)
                                                 <li>{{ $error }}</li>
                                             @endforeach
                                         </ul>
                                     </div>
                                </div>
                             </div>
                         </div>
                    @endif

                     {{ $slot }}
                </main>
           </div>
         </div>
          <!-- Script to handle dropdown toggle -->
         <script>
             document.addEventListener('DOMContentLoaded', function() {
            const profileDropdownButton = document.getElementById('profile-dropdown-button');
            const profileDropdownMenu = document.getElementById('profile-dropdown-menu');

            profileDropdownButton.addEventListener('click', function() {
                profileDropdownMenu.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!profileDropdownButton.contains(event.target) && !profileDropdownMenu.contains(event.target)) {
                    profileDropdownMenu.classList.add('hidden');
                }
            });
        });
    </script>
    </body>
</html>