<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Edit Custom Field
        </h2>
    </x-slot>
    <form action="{{ route('custom_fields.update', $customField) }}" method="POST">
        @csrf
        @method('PUT')
        <div class="mb-4">
            <label for="title" class="block text-gray-700 text-sm font-bold mb-2">Title:</label>
                <input type="text" name="title" id="title" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" value="{{ $customField->title }}" required>
        </div>
        <div class="mb-4">
            <label for="type" class="block text-gray-700 text-sm font-bold mb-2">Type:</label>
            <select name="type" id="type" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                <option value="text" @if($customField->type == 'text') selected @endif>Text</option>
                <option value="date" @if($customField->type == 'date') selected @endif>Date</option>
                <option value="radio" @if($customField->type == 'radio') selected @endif>Radio</option>
                <option value="checkbox" @if($customField->type == 'checkbox') selected @endif>Checkbox</option>
            </select>
        </div>
        <div id="options-container" class="mb-4 @if(!in_array($customField->type, ['radio','checkbox'])) hidden @endif">
            <label class="block text-gray-700 text-sm font-bold mb-2">Options (for Radio/Checkbox, one per line):</label>
            <div id="option-fields">
                @if(is_array($customField->options))
                @foreach($customField->options as $option)
                    <div class="mb-2">
                        <input type="text" name="options[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" value="{{ $option }}">
                    </div>
                    @endforeach
                @else
                    <div class="mb-2">
                        <input type="text" name="options[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    </div>
                @endif

            </div>
            <button type="button" id="add-option" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-1 px-2 rounded text-sm">Add Option</button>
        </div>
            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Update Custom Field
        </button>
    </form>
    <script>
            document.addEventListener('DOMContentLoaded', function() {
                const typeSelect = document.getElementById('type');
                const optionsContainer = document.getElementById('options-container');
                const optionFields = document.getElementById('option-fields');
                const addOptionButton = document.getElementById('add-option');

            function toggleOptionsVisibility() {
                if (typeSelect.value === 'radio' || typeSelect.value === 'checkbox') {
                    optionsContainer.classList.remove('hidden');
                } else {
                    optionsContainer.classList.add('hidden');
                }
            }
            typeSelect.addEventListener('change', toggleOptionsVisibility);
            toggleOptionsVisibility();


            addOptionButton.addEventListener('click', function() {
                const newOptionField = document.createElement('div');
                newOptionField.classList.add('mb-2');
                newOptionField.innerHTML = `
                    <input type="text" name="options[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                `;
                optionFields.appendChild(newOptionField);
            });
            });
    </script>
</x-app-layout>