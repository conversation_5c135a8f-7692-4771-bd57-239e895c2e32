<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Custom Fields
        </h2>
    </x-slot>
    <div>
        <div class="mb-4">
            <a href="{{ route('custom_fields.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Add Custom Field
            </a>
            </div>
            <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead>
                    <tr>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">Title</th>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
                </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @forelse($customFields as $field)
                <tr>
                    <td class="px-6 py-4 whitespace-no-wrap">{{ $field->title }}</td>
                    <td class="px-6 py-4 whitespace-no-wrap">{{ $field->type }}</td>
                    <td class="px-6 py-4 whitespace-no-wrap text-sm font-medium">
                            <a href="{{ route('custom_fields.edit', $field) }}" class="text-blue-600 hover:text-blue-900 mr-2">Edit</a>
                        <form action="{{ route('custom_fields.destroy', $field) }}" method="POST" class="inline-block" onsubmit="return confirm('Are you sure?');">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                            </form>
                    </td>
                    </tr>
                @empty
                    <tr>
                        <td class="px-6 py-4 whitespace-no-wrap" colspan="3">No custom fields found</td>
                    </tr>
                @endforelse
                </tbody>
            </table>
            </div>
            <div class="mt-4">
                {{ $customFields->links() }}
            </div>
    </div>
</x-app-layout>