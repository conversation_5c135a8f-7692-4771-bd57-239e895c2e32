<x-app-layout>
    <x-slot name="header">
        <h2 class="text-2xl font-bold text-gray-900 leading-tight">
            Add Purchase
        </h2>
    </x-slot>

    <div class="py-6 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <!-- Main Card -->
        <div class="bg-white rounded-lg shadow-lg p-6 transition-all duration-300">
            <form id="addPurchaseForm" method="POST" action="{{ route('purchases.store') }}" class="space-y-6">
                @csrf
                <!-- Customer Search Section -->
                <div class="relative">
                    <label for="customer_search" class="block text-gray-700 text-sm font-semibold mb-2">Search Customer</label>
                    <div class="relative">
                        <input type="text" 
                               id="customer_search" 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                               placeholder="Search by name or phone number">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                    </div>
                    <ul id="customer_results" class="hidden absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-auto">
                    </ul>
                </div>

                <!-- Customer Details Card -->
                <div id="customer_details" class="hidden bg-gray-50 rounded-lg p-6 border border-gray-200 shadow-sm space-y-3">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">Customer Details</h3>
                        <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                            Loyalty Points: <span id="customer_loyalty_points" class="font-semibold">0</span>
                        </span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <p class="text-sm"><span class="text-gray-600">Name:</span> <span id="customer_name" class="font-medium text-gray-900"></span></p>
                        <p class="text-sm"><span class="text-gray-600">Phone:</span> <span id="customer_phone" class="font-medium text-gray-900"></span></p>
                        <p class="text-sm"><span class="text-gray-600">Email:</span> <span id="customer_email" class="font-medium text-gray-900"></span></p>
                    </div>
                    <input type="hidden" name="customer_id" id="customer_id">
                </div>

                <!-- Add New Customer Button -->
                <div class="flex justify-start">
                    <button type="button" 
                            id="add_new_customer" 
                            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                        <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        Add New Customer
                    </button>
                </div>

                <!-- Add Customer Form Card -->
                <div id="add_customer_form" class="hidden">
                    <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-200 space-y-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">New Customer Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="new_customer_name" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                                <input type="text" 
                                       id="new_customer_name" 
                                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label for="new_customer_phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                                <input type="text" 
                                       id="new_customer_phone" 
                                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div class="md:col-span-2">
                                <label for="new_customer_email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                <input type="email" 
                                       id="new_customer_email" 
                                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                        <div class="flex justify-end mt-4">
                            <button type="button" 
                                    id="create_customer_button" 
                                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                                Create Customer
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Purchase Details Section -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="amount" class="block text-sm font-medium text-gray-700 mb-1">Amount</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500">₹</span>
                            </div>
                            <input type="number" 
                                   step="0.01" 
                                   name="amount" 
                                   id="amount" 
                                   class="w-full pl-7 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                   required>
                        </div>
                    </div>
                    <div>
                        <label for="discount" class="block text-sm font-medium text-gray-700 mb-1">Discount</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500">₹</span>
                            </div>
                            <input type="number" 
                                   step="0.01" 
                                   name="discount" 
                                   id="discount" 
                                   class="w-full pl-7 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>
                    <div>
                        <label for="loyalty_points_spent" class="block text-sm font-medium text-gray-700 mb-1">Loyalty Points to Spend</label>
                        <input type="number" 
                               name="loyalty_points_spent" 
                               id="loyalty_points_spent" 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end mt-6">
                    <button type="submit" 
                            class="px-6 py-3 bg-blue-600 text-white font-medium rounded-lg shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                        Register Purchase
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const customerSearch = document.getElementById('customer_search');
            const customerResults = document.getElementById('customer_results');
            const customerDetails = document.getElementById('customer_details');
            const customerName = document.getElementById('customer_name');
            const customerPhone = document.getElementById('customer_phone');
            const customerEmail = document.getElementById('customer_email');
            const customerIdInput = document.getElementById('customer_id');
            const customerLoyaltyPoints = document.getElementById('customer_loyalty_points');
            const addNewCustomerButton = document.getElementById('add_new_customer');
            const addCustomerForm = document.getElementById('add_customer_form');
            const createCustomerButton = document.getElementById('create_customer_button');
            const addPurchaseForm = document.getElementById('addPurchaseForm');

            // Toggle Add Customer Form
            addNewCustomerButton.addEventListener('click', function() {
                if (addCustomerForm.classList.contains('hidden')) {
                    // Show form with animation
                    addCustomerForm.classList.remove('hidden');
                    addCustomerForm.style.opacity = '0';
                    setTimeout(() => {
                        addCustomerForm.style.opacity = '1';
                    }, 50);
                } else {
                    // Hide form with animation
                    addCustomerForm.style.opacity = '0';
                    setTimeout(() => {
                        addCustomerForm.classList.add('hidden');
                    }, 200);
                }
                customerDetails.classList.add('hidden');
                customerResults.classList.add('hidden');
            });

            customerSearch.addEventListener('input', async function() {
                const searchTerm = this.value.trim();
                if (searchTerm.length < 2) {
                    customerResults.classList.add('hidden');
                    return;
                }

                try {
                    const response = await fetch('{{ route('purchases.search.customer') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({search: searchTerm})
                    });
                    const customers = await response.json();

                    customerResults.innerHTML = '';
                    if (customers.length > 0) {
                        customerResults.classList.remove('hidden');
                        customers.forEach(customer => {
                            const li = document.createElement('li');
                            li.classList.add('px-4', 'py-2', 'hover:bg-gray-50', 'cursor-pointer', 'transition-colors', 'duration-150');
                            li.textContent = `${customer.name} (${customer.phone_number})`;
                            li.addEventListener('click', async function() {
                                customerName.textContent = customer.name;
                                customerPhone.textContent = customer.phone_number;
                                customerEmail.textContent = customer.email || 'N/A';
                                customerIdInput.value = customer.id;
                                
                                // Show customer details with fade-in animation
                                customerDetails.style.opacity = '0';
                                customerDetails.classList.remove('hidden');
                                setTimeout(() => {
                                    customerDetails.style.opacity = '1';
                                }, 50);
                                
                                customerResults.classList.add('hidden');
                                customerSearch.value = '';

                                try {
                                    const response = await fetch('{{ route('purchases.search.customer') }}', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json',
                                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                        },
                                        body: JSON.stringify({search: searchTerm})
                                    });
                                    const loyaltyCustomers = await response.json();
                                    const loyaltyCustomer = loyaltyCustomers.find(c => c.id === customer.id);
                                    customerLoyaltyPoints.textContent = loyaltyCustomer?.loyalty_points?.points || 0;
                                } catch (error) {
                                    console.log('Error getting loyalty points', error);
                                }
                            });
                            customerResults.appendChild(li);
                        });
                    } else {
                        customerResults.classList.add('hidden');
                    }
                } catch (error) {
                    console.error('Error fetching customers', error);
                }
            });

            createCustomerButton.addEventListener('click', async function() {
                const newCustomerName = document.getElementById('new_customer_name').value;
                const newCustomerPhone = document.getElementById('new_customer_phone').value;
                const newCustomerEmail = document.getElementById('new_customer_email').value;

                try {
                    const response = await fetch('{{ route('purchases.create.customer') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            name: newCustomerName,
                            phone_number: newCustomerPhone,
                            email: newCustomerEmail
                        })
                    });
                    const customer = await response.json();
                    
                    // Update customer details with animation
                    customerName.textContent = customer.name;
                    customerPhone.textContent = customer.phone_number;
                    customerEmail.textContent = customer.email || 'N/A';
                    customerIdInput.value = customer.id;
                    customerLoyaltyPoints.textContent = 0;
                    
                    // Show customer details with animation
                    customerDetails.style.opacity = '0';
                    customerDetails.classList.remove('hidden');
                    setTimeout(() => {
                       customerDetails.style.opacity = '1';
                    }, 50);

                    // Hide add customer form with animation
                    addCustomerForm.style.opacity = '0';
                    setTimeout(() => {
                        addCustomerForm.classList.add('hidden');
                        // Clear form fields
                        document.getElementById('new_customer_name').value = '';
                        document.getElementById('new_customer_phone').value = '';
                        document.getElementById('new_customer_email').value = '';
                    }, 200);

                } catch (error) {
                    console.error('Error creating customer', error);
                    // Show error notification
                    showNotification('Error creating customer. Please try again.', 'error');
                }
            });

            addPurchaseForm.addEventListener('submit', function(event) {
                if (!customerIdInput.value) {
                    event.preventDefault();
                    showNotification('Please select or create a customer before registering the purchase', 'warning');
                    return;
                }

                const loyaltyPointsSpent = parseInt(document.getElementById('loyalty_points_spent').value) || 0;
                const currentLoyaltyPoints = parseInt(customerLoyaltyPoints.textContent) || 0;

                if (loyaltyPointsSpent > currentLoyaltyPoints) {
                    event.preventDefault();
                    showNotification('Cannot redeem more loyalty points than available', 'warning');
                    return;
                }
            });

            // Add notification system
            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
                    type === 'error' ? 'bg-red-500 text-white' :
                    type === 'warning' ? 'bg-yellow-500 text-white' :
                    'bg-blue-500 text-white'
                }`;
                notification.textContent = message;

                document.body.appendChild(notification);

                // Animate in
                setTimeout(() => {
                    notification.style.transform = 'translateX(0)';
                }, 100);

                // Animate out and remove
                setTimeout(() => {
                    notification.style.transform = 'translateX(full)';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }, 3000);
            }

            // Add input animations
            const inputs = document.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('ring-2', 'ring-blue-200');
                });
                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('ring-2', 'ring-blue-200');
                });
            });
        });
    </script>

    <style>
        /* Smooth transitions */
        .transition-all {
            transition-property: all;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 200ms;
        }

        /* Fade animations */
        #customer_details, #add_customer_form {
            transition: opacity 200ms ease-in-out;
        }

        /* Custom scrollbar for results */
        #customer_results {
            scrollbar-width: thin;
            scrollbar-color: #CBD5E0 #F7FAFC;
        }

        #customer_results::-webkit-scrollbar {
            width: 8px;
        }

        #customer_results::-webkit-scrollbar-track {
            background: #F7FAFC;
        }

        #customer_results::-webkit-scrollbar-thumb {
            background-color: #CBD5E0;
            border-radius: 4px;
        }
    </style>
</x-app-layout>