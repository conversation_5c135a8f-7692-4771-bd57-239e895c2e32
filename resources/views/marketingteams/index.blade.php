<x-app-layout>
    <x-slot name="header">
       <h2 class="font-semibold text-xl text-gray-800 leading-tight">
           Marketing Team Members
      </h2>
  </x-slot>

   <div class="py-12">
       <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
          <div class="mb-4">
                <a href="{{ route('marketingteams.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Add Team Member</a>
            </div>
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
               <div class="p-6 bg-white border-b border-gray-200">
                  <table class="min-w-full divide-y divide-gray-200">
                       <thead class="bg-gray-50">
                          <tr>
                              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                               <th></th>
                           </tr>
                      </thead>
                       <tbody class="bg-white divide-y divide-gray-200">
                          @foreach ($teamMembers as $member)
                               <tr>
                                  <td class="px-6 py-4 whitespace-nowrap">{{ $member->name }}</td>
                                   <td class="px-6 py-4 whitespace-nowrap">{{ $member->email }}</td>
                                   <td>
                                       <form action="{{ route('marketingteams.destroy', $member) }}" method="POST" onsubmit="return confirm('Are you sure?');">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                       </form>
                                  </td>
                               </tr>
                          @endforeach
                       </tbody>
                  </table>
              </div>
            </div>
       </div>
   </div>
</x-app-layout>