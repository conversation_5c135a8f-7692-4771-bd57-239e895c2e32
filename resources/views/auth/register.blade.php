<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Register - {{ config('app.name', 'Laravel') }}</title>
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="bg-gray-50 antialiased">
    <div class="min-h-screen flex">
        <!-- Left Section with Gradient Background -->
        <div class="hidden lg:flex lg:w-1/2 relative overflow-hidden">
            <!-- Gradient Overlay -->
            <div class="absolute inset-0 bg-gradient-to-br from-blue-600 to-blue-800 opacity-90"></div>
            
            <!-- Background Pattern -->
            <div class="absolute inset-0 bg-grid-pattern opacity-10"></div>

            <!-- Abstract Shapes -->
            <div class="absolute -bottom-32 -left-40 w-80 h-80 bg-white rounded-full opacity-10"></div>
            <div class="absolute -top-40 -right-40 w-96 h-96 bg-white rounded-full opacity-10"></div>

            <!-- Content -->
            <div class="relative z-10 flex flex-col justify-center px-16 text-white">
                <h1 class="text-5xl font-bold mb-6">Welcome to Our Platform</h1>
                <p class="text-xl text-blue-100">Join thousands of businesses who trust us to manage their customer relationships effectively.</p>
            </div>
        </div>

        <!-- Right Section with Form -->
        <div class="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24">
            <div class="mx-auto w-full max-w-sm">
                <!-- Logo -->
                <div class="mb-10">
                    <img src="{{ asset('images/logo.jpg') }}" alt="Logo" class="h-12 w-auto mx-auto">
                </div>
                
                <div class="bg-white py-8 px-8 shadow-xl rounded-xl">
                    <h2 class="text-2xl font-bold text-gray-900 text-center mb-8">Create your account</h2>

                    <form method="POST" action="{{ route('register') }}" class="space-y-6">
                        @csrf
                        
                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700">Full Name</label>
                            <input id="name" 
                                   type="text" 
                                   name="name" 
                                   value="{{ old('name') }}"
                                   required 
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                                   placeholder="John Doe">
                            @error('name')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700">Email address</label>
                            <input id="email" 
                                   type="email" 
                                   name="email" 
                                   value="{{ old('email') }}"
                                   required 
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                                   placeholder="<EMAIL>">
                            @error('email')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Phone Number -->
                        <div>
                            <label for="phone_number" class="block text-sm font-medium text-gray-700">Phone Number</label>
                            <input id="phone_number" 
                                   type="tel" 
                                   name="phone_number" 
                                   value="{{ old('phone_number') }}"
                                   required 
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                                   placeholder="+****************">
                            @error('phone_number')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Company Name -->
                        <div>
                            <label for="company_name" class="block text-sm font-medium text-gray-700">Company Name</label>
                            <input id="company_name" 
                                   type="text" 
                                   name="company_name" 
                                   value="{{ old('company_name') }}"
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                                   placeholder="Your Company Ltd.">
                            @error('company_name')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Google Review Link -->
                        <div>
                            <label for="google_review_link" class="block text-sm font-medium text-gray-700">Google Review Link (Optional)</label>
                            <input id="google_review_link" 
                                   type="url" 
                                   name="google_review_link" 
                                   value="{{ old('google_review_link') }}"
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                                   placeholder="https://g.page/r/...">
                            @error('google_review_link')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Password -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                            <input id="password" 
                                   type="password" 
                                   name="password" 
                                   required 
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                                   placeholder="••••••••">
                            @error('password')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Confirm Password -->
                        <div>
                            <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                            <input id="password_confirmation" 
                                   type="password" 
                                   name="password_confirmation" 
                                   required 
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                                   placeholder="••••••••">
                        </div>

                        <div>
                            <button type="submit" 
                                    class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-semibold text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                                Create Account
                            </button>
                        </div>
                    </form>

                    <p class="mt-6 text-center text-sm text-gray-600">
                        Already have an account? 
                        <a href="{{ route('login') }}" class="font-medium text-blue-600 hover:text-blue-500">
                            Sign in
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <style>
        .bg-grid-pattern {
            background-image: 
                linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 40px 40px;
        }
    </style>
</body>
</html>