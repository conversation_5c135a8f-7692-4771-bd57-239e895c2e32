<x-app-layout>
   <x-slot name="header">
       <h2 class="font-semibold text-xl text-gray-800 leading-tight">
           Configure Front Desk Credentials
       </h2>
   </x-slot>

   <div class="py-12">
       <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
           <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
               <form action="{{ route('frontdesk.credentials.store') }}" method="POST">
                   @csrf
                   <div class="mb-4">
                       <label for="frontdesk_username" class="block text-gray-700 text-sm font-bold mb-2">Front Desk Username:</label>
                       <input type="text" name="frontdesk_username" id="frontdesk_username" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                    </div>
                   <div class="mb-4">
                       <label for="frontdesk_password" class="block text-gray-700 text-sm font-bold mb-2">Front Desk Password:</label>
                       <input type="password" name="frontdesk_password" id="frontdesk_password" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                  </div>
                  <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Save Credentials</button>
               </form>
                <p class="mt-4">Front end can be logged in through <a href="{{ route('frontdesk.login.show') }}">https://domain/frontdesk</a></p>
           </div>
       </div>
   </div>
 </x-app-layout>