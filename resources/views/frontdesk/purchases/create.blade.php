<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Purchase</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <style>
        .form-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .search-results {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 0.375rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .customer-details-card {
            background-color: #f9fafb;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #3b82f6;
        }
        
        .input-focus:focus {
            border-color: #3b82f6;
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
        }
        
        .transition-all {
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen py-12 px-4 sm:px-6 lg:px-8">
    <div class="form-container">
        <h2 class="text-2xl font-bold text-gray-800 mb-8 border-b pb-4">
            Add Purchase
        </h2>
        
        <form id="addPurchaseForm" method="POST" action="{{ route('purchases.store') }}" class="space-y-6">
            @csrf
            
            <!-- Customer Search Section -->
            <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-200">
                <div class="mb-4">
                    <label for="customer_search" class="block text-gray-700 text-sm font-semibold mb-2">Search Customer</label>
                    <div class="relative">
                        <input type="text" id="customer_search" 
                            class="shadow-sm appearance-none border border-gray-300 rounded-lg w-full py-2.5 px-4 text-gray-700 leading-tight input-focus transition-all" 
                            placeholder="Enter name or phone number...">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <ul id="customer_results" class="hidden search-results bg-white mt-2 z-10">
                    </ul>
                </div>
                
                <!-- Customer Details Card -->
                <div id="customer_details" class="hidden customer-details-card">
                    <h3 class="font-semibold text-lg mb-3 text-gray-800">Customer Details</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <p class="text-sm"><span class="text-gray-500">Name:</span> <span id="customer_name" class="font-medium ml-1"></span></p>
                        <p class="text-sm"><span class="text-gray-500">Phone:</span> <span id="customer_phone" class="font-medium ml-1"></span></p>
                        <p class="text-sm"><span class="text-gray-500">Email:</span> <span id="customer_email" class="font-medium ml-1"></span></p>
                        <p class="text-sm">
                            <span class="text-gray-500">Loyalty Points:</span> 
                            <span id="customer_loyalty_points" class="font-medium ml-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs bg-blue-100 text-blue-800"></span>
                        </p>
                    </div>
                    <input type="hidden" name="customer_id" id="customer_id">
                </div>
                
                <!-- Add New Customer Button -->
                <div class="flex justify-start mt-4">
                    <button type="button" id="add_new_customer" 
                        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all">
                        <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                        </svg>
                        Add New Customer
                    </button>
                </div>
            </div>
            
            <!-- New Customer Form -->
            <div id="add_customer_form" class="hidden bg-white p-5 rounded-lg shadow-sm border border-gray-200 space-y-4">
                <h3 class="font-semibold text-lg mb-4 text-gray-800">Create New Customer</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="new_customer_name" class="block text-gray-700 text-sm font-medium mb-2">Full Name</label>
                        <input type="text" id="new_customer_name" 
                            class="shadow-sm appearance-none border border-gray-300 rounded-lg w-full py-2.5 px-4 text-gray-700 leading-tight input-focus transition-all" 
                            placeholder="John Doe">
                    </div>
                    <div>
                        <label for="new_customer_phone" class="block text-gray-700 text-sm font-medium mb-2">Phone Number</label>
                        <input type="text" id="new_customer_phone" 
                            class="shadow-sm appearance-none border border-gray-300 rounded-lg w-full py-2.5 px-4 text-gray-700 leading-tight input-focus transition-all" 
                            placeholder="+****************">
                    </div>
                    <div class="md:col-span-2">
                        <label for="new_customer_email" class="block text-gray-700 text-sm font-medium mb-2">Email Address</label>
                        <input type="email" id="new_customer_email" 
                            class="shadow-sm appearance-none border border-gray-300 rounded-lg w-full py-2.5 px-4 text-gray-700 leading-tight input-focus transition-all" 
                            placeholder="<EMAIL>">
                    </div>
                </div>
                <div class="flex justify-end mt-4">
                    <button type="button" id="create_customer_button" 
                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all">
                        Create Customer
                    </button>
                </div>
            </div>
            
            <!-- Purchase Details Section -->
            <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-200">
                <h3 class="font-semibold text-lg mb-6 text-gray-800">Purchase Details</h3>
                <div class="space-y-6">
                    <div>
                        <label for="amount" class="block text-gray-700 text-sm font-medium mb-2">Amount ($)</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">$</span>
                            </div>
                            <input type="number" step="0.01" name="amount" id="amount" required
                                class="shadow-sm pl-7 appearance-none border border-gray-300 rounded-lg w-full py-2.5 px-4 text-gray-700 leading-tight input-focus transition-all" 
                                placeholder="0.00">
                        </div>
                    </div>
                    
                    <div>
                        <label for="discount" class="block text-gray-700 text-sm font-medium mb-2">Discount ($)</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">$</span>
                            </div>
                            <input type="number" step="0.01" name="discount" id="discount"
                                class="shadow-sm pl-7 appearance-none border border-gray-300 rounded-lg w-full py-2.5 px-4 text-gray-700 leading-tight input-focus transition-all" 
                                placeholder="0.00">
                        </div>
                    </div>
                    
                    <div>
                        <label for="loyalty_points_spent" class="block text-gray-700 text-sm font-medium mb-2">Loyalty Points to Redeem</label>
                        <input type="number" name="loyalty_points_spent" id="loyalty_points_spent"
                            class="shadow-sm appearance-none border border-gray-300 rounded-lg w-full py-2.5 px-4 text-gray-700 leading-tight input-focus transition-all" 
                            placeholder="0">
                    </div>
                </div>
            </div>
            
            <!-- Submit Button -->
            <div class="flex justify-end">
                <button type="submit" 
                    class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all">
                    <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Register Purchase
                </button>
            </div>
        </form>
    </div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const customerSearch = document.getElementById('customer_search');
        const customerResults = document.getElementById('customer_results');
        const customerDetails = document.getElementById('customer_details');
        const customerName = document.getElementById('customer_name');
        const customerPhone = document.getElementById('customer_phone');
        const customerEmail = document.getElementById('customer_email');
        const customerIdInput = document.getElementById('customer_id');
        const customerLoyaltyPoints = document.getElementById('customer_loyalty_points');
        const addNewCustomerButton = document.getElementById('add_new_customer');
        const addCustomerForm = document.getElementById('add_customer_form');
        const createCustomerButton = document.getElementById('create_customer_button');
        const addPurchaseForm = document.getElementById('addPurchaseForm');

        customerSearch.addEventListener('input', async function() {
            const searchTerm = this.value.trim();
            if(searchTerm.length < 2){
                customerResults.classList.add('hidden');
                return;
            }

            try {
                const response = await fetch('{{ route('purchases.search.customer') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({search: searchTerm})
                });
                const customers = await response.json();

                customerResults.innerHTML = '';
                if (customers.length > 0) {
                    customerResults.classList.remove('hidden');
                    customers.forEach(customer => {
                        const li = document.createElement('li');
                        li.classList.add('p-3', 'hover:bg-blue-50', 'cursor-pointer', 'transition-all', 'border-b', 'border-gray-100');
                        li.innerHTML = `
                            <div class="flex items-center">
                                <div class="flex-shrink-0 bg-gray-100 rounded-full p-2">
                                    <svg class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">${customer.name}</p>
                                    <p class="text-sm text-gray-500">${customer.phone_number}</p>
                                </div>
                            </div>
                        `;
                        li.addEventListener('click', async function(){
                            customerName.textContent = customer.name;
                            customerPhone.textContent = customer.phone_number;
                            customerEmail.textContent = customer.email || 'N/A';
                            customerIdInput.value = customer.id;
                            customerDetails.classList.remove('hidden');
                            customerResults.classList.add('hidden');
                            customerSearch.value = '';

                            try{
                                const response = await fetch('{{ route('purchases.search.customer') }}',{
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                    },
                                    body: JSON.stringify({search: searchTerm})
                                });
                                const loyaltyCustomers = await response.json();
                                const loyaltyCustomer = loyaltyCustomers.find(c => c.id === customer.id);
                                customerLoyaltyPoints.textContent = loyaltyCustomer?.loyalty_points?.points || 0;

                            } catch (error){
                                console.log('Error getting loyalty points', error);
                            }
                        });
                        customerResults.appendChild(li);
                    });
                } else {
                    const li = document.createElement('li');
                    li.classList.add('p-3', 'text-sm', 'text-gray-500', 'text-center');
                    li.textContent = 'No customers found';
                    customerResults.classList.remove('hidden');
                    customerResults.appendChild(li);
                }
            }catch (error){
                console.error('Error fetching customers', error);
            }
        });
        
        addNewCustomerButton.addEventListener('click', function(){
            addCustomerForm.classList.remove('hidden');
            addCustomerForm.classList.add('animate-fade-in');
            customerDetails.classList.add('hidden');
            customerResults.classList.add('hidden');
        });

        createCustomerButton.addEventListener('click', async function(){
            const newCustomerName = document.getElementById('new_customer_name').value;
            const newCustomerPhone = document.getElementById('new_customer_phone').value;
            const newCustomerEmail = document.getElementById('new_customer_email').value;
            
            if (!newCustomerName || !newCustomerPhone) {
                alert('Please fill in at least name and phone number');
                return;
            }
            
            try{
                const response = await fetch('{{ route('purchases.create.customer') }}',{
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        name: newCustomerName,
                        phone_number: newCustomerPhone,
                        email: newCustomerEmail
                    })
                });
                const customer = await response.json();
                customerName.textContent = customer.name;
                customerPhone.textContent = customer.phone_number;
                customerEmail.textContent = customer.email || 'N/A';
                customerIdInput.value = customer.id;
                customerLoyaltyPoints.textContent = 0;
                customerDetails.classList.remove('hidden');
                addCustomerForm.classList.add('hidden');
                document.getElementById('new_customer_name').value = '';
                document.getElementById('new_customer_phone').value = '';
                document.getElementById('new_customer_email').value = '';
                
                // Show success message
                const alertDiv = document.createElement('div');
                alertDiv.classList.add('mb-4', 'p-3', 'bg-green-100', 'text-green-700', 'rounded-md', 'flex', 'items-center');
                alertDiv.innerHTML = `
                    <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>Customer created successfully!</span>
                `;
                customerDetails.parentNode.insertBefore(alertDiv, customerDetails);
                
                setTimeout(() => {
                    alertDiv.remove();
                }, 3000);
                
            } catch (error){
                console.error('Error creating customers', error);
                alert('Error creating customer. Please try again.');
            }
        });
        
        addPurchaseForm.addEventListener('submit', function(event){
            if(!customerIdInput.value){
                event.preventDefault();
                
                // Show error message
                const alertDiv = document.createElement('div');
                alertDiv.classList.add('mb-4', 'p-3', 'bg-red-100', 'text-red-700', 'rounded-md', 'flex', 'items-center');
                alertDiv.innerHTML = `
                    <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>Please select or create a customer before registering the purchase</span>
                `;
                document.querySelector('form').prepend(alertDiv);
                
                setTimeout(() => {
                    alertDiv.remove();
                }, 3000);
                
                return;
            }
            
            const loyaltyPointsSpent = parseInt(document.getElementById('loyalty_points_spent').value) || 0;
            const currentLoyaltyPoints = parseInt(customerLoyaltyPoints.textContent) || 0;
            
            if (loyaltyPointsSpent > currentLoyaltyPoints) {
                event.preventDefault();
                
                // Show error message
                const alertDiv = document.createElement('div');
                alertDiv.classList.add('mb-4', 'p-3', 'bg-red-100', 'text-red-700', 'rounded-md', 'flex', 'items-center');
                alertDiv.innerHTML = `
                    <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>Cannot redeem more loyalty points than available (${currentLoyaltyPoints})</span>
                `;
                document.querySelector('form').prepend(alertDiv);
                
                setTimeout(() => {
                    alertDiv.remove();
                }, 3000);
            }
        });
        
        // Close search results when clicking outside
        document.addEventListener('click', function(event) {
            if (!customerSearch.contains(event.target) && !customerResults.contains(event.target)) {
                customerResults.classList.add('hidden');
            }
        });
    });
</script>
</body>
</html>