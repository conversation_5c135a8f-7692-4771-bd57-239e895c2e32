<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Enquiry</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <style>
        .form-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .input-focus:focus {
            border-color: #3b82f6;
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
        }
        
        .transition-all {
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen py-12 px-4 sm:px-6 lg:px-8">
    <div class="form-container">
    <h2 class="font-semibold text-xl text-gray-800 leading-tight">
  Add Enquiry
 </h2>
 <form action="{{ route('enquiries.store') }}" method="POST">
      @csrf
     <div class="mb-4">
          <label for="name" class="block text-gray-700 text-sm font-bold mb-2">Name:</label>
          <input type="text" name="name" id="name" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
     </div>
     <div class="mb-4">
          <label for="phone_number" class="block text-gray-700 text-sm font-bold mb-2">Phone Number:</label>
          <input type="text" name="phone_number" id="phone_number" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
      </div>

      @foreach($questions as $question)
          <div class="mb-4">
              <label for="question_{{ $question->id }}" class="block text-gray-700 text-sm font-bold mb-2">{{ $question->title }}</label>
              @if($question->type == 'text')
                  <input type="text" name="responses[{{ $question->id }}]" id="question_{{ $question->id }}" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
              @elseif($question->type == 'date')
                  <input type="date" name="responses[{{ $question->id }}]" id="question_{{ $question->id }}" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
              @elseif($question->type == 'radio' && is_array($question->options))
                 @foreach($question->options as $option)
                      <div class="mb-1">
                          <input type="radio" name="responses[{{ $question->id }}]" value="{{ $option }}" id="question_{{ $question->id }}_{{ $option }}">
                          <label for="question_{{ $question->id }}_{{ $option }}">{{ $option }}</label>
                      </div>
                 @endforeach
              @elseif($question->type == 'checkbox' && is_array($question->options))
                  @foreach($question->options as $option)
                       <div class="mb-1">
                          <input type="checkbox" name="responses[{{ $question->id }}][]" value="{{ $option }}" id="question_{{ $question->id }}_{{ $option }}">
                         <label for="question_{{ $question->id }}_{{ $option }}">{{ $option }}</label>
                      </div>
                 @endforeach
              @endif
          </div>
     @endforeach

     <div class="mb-4">
         <label for="lead_type" class="block text-gray-700 text-sm font-bold mb-2">Lead Type:</label>
          <select name="lead_type" id="lead_type" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
              <option value="hot">Hot</option>
             <option value="mild">Mild</option>
              <option value="cold">Cold</option>
          </select>
     </div>

      <div class="mb-4">
          <label for="additional_notes" class="block text-gray-700 text-sm font-bold mb-2">Additional Notes:</label>
          <textarea name="additional_notes" id="additional_notes" rows="3" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"></textarea>
      </div>

      <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
         Add Enquiry
      </button>
 </form>
    </div>
</body>
</html>