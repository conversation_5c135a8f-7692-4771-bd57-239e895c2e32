<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Front Desk Dashboard</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased">
    <div class="min-h-screen bg-gradient-to-b from-gray-50 to-gray-200 flex flex-col justify-center items-center p-6">
        <!-- Logo and header section -->
        <div class="mb-10 text-center">
            <img src="{{ asset('images/logo.jpg') }}" alt="Company Logo" class="h-20 w-auto mx-auto mb-6 rounded-lg shadow-md">
            <h1 class="text-4xl font-bold text-gray-800">Welcome to the Front Desk</h1>
            <p class="text-gray-600 mt-2">Select an option to continue</p>
        </div>
        
        <!-- Main buttons - keeping just the two you need -->
        <div class="flex flex-col sm:flex-row gap-6 w-full max-w-md">
            <a href="{{ route('frontdesk.accounting') }}" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-6 px-8 rounded-xl shadow-lg flex-1 flex flex-col items-center justify-center transition-all duration-300 hover:scale-105">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="text-xl">Accounting</span>
            </a>
            
            <a href="{{ route('frontdesk.enquiry') }}" class="bg-green-500 hover:bg-green-600 text-white font-bold py-6 px-8 rounded-xl shadow-lg flex-1 flex flex-col items-center justify-center transition-all duration-300 hover:scale-105">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="text-xl">Enquiry</span>
            </a>
        </div>
    </div>
</body>
</html>