<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-2xl text-gray-800 leading-tight">
            Dashboard Overview
        </h2>
    </x-slot>

    <div class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Summary Cards Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <!-- Total Customers Card -->
                <div class="bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl shadow-lg p-6 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-blue-100">Total Customers</p>
                            <h3 class="text-3xl font-bold mt-1">{{ $totalCustomers }}</h3>
                        </div>
                        <div class="bg-blue-500 bg-opacity-40 p-3 rounded-lg">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Total Sales Card -->
                <div class="bg-gradient-to-br from-green-600 to-green-700 rounded-xl shadow-lg p-6 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-green-100">Total Sales</p>
                            <h3 class="text-3xl font-bold mt-1">₹{{ number_format($totalSales, 2) }}</h3>
                        </div>
                        <div class="bg-green-500 bg-opacity-40 p-3 rounded-lg">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Sales Trend Card -->
                <div class="bg-white rounded-xl shadow-lg p-6 transition-all duration-200 hover:shadow-xl border border-gray-100">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">Sales Trend</h3>
                    <div class="h-80">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>

                <!-- Customer Status Card -->
                <div class="bg-white rounded-xl shadow-lg p-6 transition-all duration-200 hover:shadow-xl border border-gray-100">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">Customer Status</h3>
                    <div class="h-80">
                        <canvas id="customerChart"></canvas>
                    </div>
                </div>

                <!-- Customers by Tag Card -->
                <div class="bg-white rounded-xl shadow-lg p-6 md:col-span-2 transition-all duration-200 hover:shadow-xl border border-gray-100">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">Customers by Tag</h3>
                    <div class="h-96">
                        <canvas id="tagChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Prepare data from PHP variables
            const salesLabels = {!! json_encode($salesLabels) !!};
            const salesValues = {!! json_encode($salesValues) !!};
            const activeCustomersCount = {{ $activeCustomersCount }};
            const inactiveCustomersCount = {{ $inactiveCustomersCount }};
            const tagLabels = {!! json_encode($tagLabels) !!};
            const tagValues = {!! json_encode($tagValues) !!};

            // Chart.js defaults for consistent styling
            Chart.defaults.font.family = 'Inter var, system-ui, -apple-system, sans-serif';
            Chart.defaults.color = '#64748b';
            Chart.defaults.borderColor = '#e2e8f0';
            
            // Sales Line Chart
            new Chart(document.getElementById('salesChart').getContext('2d'), {
                type: 'line',
                data: {
                    labels: salesLabels,
                    datasets: [{
                        label: 'Sales Volume',
                        data: salesValues,
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: 'rgba(59, 130, 246, 1)',
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                drawBorder: false
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // Customer Status Doughnut Chart
            new Chart(document.getElementById('customerChart').getContext('2d'), {
                type: 'doughnut',
                data: {
                    labels: ['Active', 'Inactive'],
                    datasets: [{
                        data: [activeCustomersCount, inactiveCustomersCount],
                        backgroundColor: [
                            'rgba(34, 197, 94, 0.9)',
                            'rgba(239, 68, 68, 0.9)'
                        ],
                        borderWidth: 0,
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '75%',
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Tags Bar Chart
            new Chart(document.getElementById('tagChart').getContext('2d'), {
                type: 'bar',
                data: {
                    labels: tagLabels,
                    datasets: [{
                        label: 'Customers',
                        data: tagValues,
                        backgroundColor: tagLabels.map((_, index) => {
                            const hue = (index * 137.5) % 360;
                            return `hsla(${hue}, 70%, 60%, 0.8)`;
                        }),
                        borderRadius: 6,
                        borderWidth: 0,
                        maxBarThickness: 40
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                drawBorder: false
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        });
    </script>

    
</x-app-layout>
