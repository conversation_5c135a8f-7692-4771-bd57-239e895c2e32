<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-2xl text-gray-800 leading-tight">
            Customer Reviews Management
        </h2>
    </x-slot>

    <!-- Copy Alert -->
    <div id="copyAlert" class="fixed top-4 right-4 transform translate-y-[-100%] opacity-0 transition-all duration-300 z-50">
        <div class="bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center space-x-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>Link copied to clipboard!</span>
        </div>
    </div>

    <div class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Review Link Card -->
            <div class="bg-white rounded-xl shadow-lg p-8 border border-gray-100 mb-8 transform hover:shadow-xl transition-all duration-300">
                <div class="flex items-center mb-6">
                    <div class="p-3 bg-blue-50 rounded-lg mr-4">
                        <svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800">Share Your Review Link</h3>
                </div>
                
                <div class="flex flex-col sm:flex-row items-center gap-3">
                    <div class="relative flex-grow">
                        <input 
                            type="text" 
                            value="{{ route('reviews.review_page', Auth::id()) }}" 
                            id="reviewLink" 
                            class="w-full px-4 py-3 rounded-lg border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 text-gray-900 pr-4 font-medium" 
                            readonly
                        >
                    </div>
                    <button 
                        onclick="copyToClipboard()"
                        class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200 transform hover:scale-105 w-full sm:w-auto justify-center"
                    >
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                        Copy Link
                    </button>
                </div>
            </div>

            <!-- Negative Reviews Card -->
            <div class="bg-white rounded-xl shadow-lg p-8 border border-gray-100">
                <div class="flex items-center mb-6">
                    <div class="p-3 bg-red-50 rounded-lg mr-4">
                        <svg class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800">Negative Reviews (3 stars or less)</h3>
                </div>

                <ul class="space-y-4">
                    @forelse($reviews as $review)
                        <li class="bg-gray-50 p-6 rounded-lg hover:bg-gray-100 transition-all duration-200 border border-gray-100">
                            <div class="flex items-start justify-between mb-3">
                                <div>
                                    <p class="font-semibold text-gray-900 mb-1">
                                        {{ $review->name ?? 'Anonymous User' }}
                                    </p>
                                    <p class="text-sm text-gray-600">{{ $review->phone_number ?? 'No phone number provided' }}</p>
                                </div>
                                <div class="flex items-center bg-red-100 px-3 py-1 rounded-full">
                                    <svg class="w-4 h-4 text-red-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                    <span class="text-sm font-medium text-red-700">{{ $review->rating }}/5</span>
                                </div>
                            </div>
                            <p class="text-gray-700 bg-white p-4 rounded-lg border border-gray-100">
                                {{ $review->review ?? 'No review content provided' }}
                            </p>
                        </li>
                    @empty
                        <li class="text-center py-8">
                            <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                            <p class="text-gray-500 text-lg">No negative reviews found</p>
                        </li>
                    @endforelse
                </ul>

                <!-- Pagination -->
                <div class="mt-8">
                    {{ $reviews->links() }}
                </div>
            </div>
        </div>
    </div>

    <script>
        function copyToClipboard() {
            navigator.clipboard.writeText(document.getElementById('reviewLink').value);
            const alert = document.getElementById('copyAlert');
            alert.style.transform = 'translate(0, 0)';
            alert.style.opacity = '1';
            
            setTimeout(() => {
                alert.style.transform = 'translate(0, -100%)';
                alert.style.opacity = '0';
            }, 3000);
        }
    </script>
</x-app-layout>