<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
     <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Google Review</title>
     <script src="https://cdn.tailwindcss.com"></script>
     <script src="https://unpkg.com/lucide@latest"></script>
  </head>
 <body class="min-h-screen bg-[#202124] p-4">
      <div class="max-w-2xl mx-auto p-6 bg-[#202124] text-gray-200 rounded-lg">
         <div class="mb-6">
               <h1 class="text-2xl font-semibold mb-4">{{ $user->company_name ?? 'ConnectEzee' }}</h1>
              <div class="flex items-center gap-3 mb-4">
                 <div class="w-12 h-12 rounded-full bg-gray-600 flex items-center justify-center text-white text-xl font-semibold">
                      U
                  </div>
                  <div>
                      <div class="text-lg font-semibold">User</div>
                       <div class="text-gray-400 flex items-center text-sm">
                          Posting publicly across Google
                          <i data-lucide="info" class="w-5 h-5 ml-2 text-gray-400"></i>
                      </div>
                 </div>
             </div>
         </div>

         <form id="reviewForm" class="space-y-6">
              <div class="flex gap-1" id="starsContainer">
                  <!-- Stars will be added by JavaScript -->
             </div>

             <textarea
                  id="review"
                 placeholder="Share details of your experience..."
                 class="w-full min-h-[100px] bg-transparent border border-gray-600 rounded-lg p-3 focus:border-blue-500 text-gray-200 focus:outline-none resize-none"
             ></textarea>

              <div id="additionalFields" class="space-y-4 hidden">
                  <input
                     type="text"
                      id="name"
                      placeholder="Your Name"
                     class="w-full bg-transparent border border-gray-600 rounded-lg p-3 focus:border-blue-500 text-gray-200 focus:outline-none"
                 >
                  <input
                      type="tel"
                      id="phone"
                      placeholder="Phone Number"
                      class="w-full bg-transparent border border-gray-600 rounded-lg p-3 focus:border-blue-500 text-gray-200 focus:outline-none"
                  >
              </div>

               <input type="hidden" name="user_id" id="user_id" value="{{ $user->id }}">
              <button
                  type="submit"
                  class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-full text-lg font-medium transition-colors"
               >
                  Post
              </button>
          </form>
      </div>

      <script>
          const googleReviewLink = '{{ $user->google_review_link }}';
           let currentRating = 0;

          // Initialize Lucide icons
          lucide.createIcons();

          // Create stars
          const starsContainer = document.getElementById('starsContainer');
          for (let i = 1; i <= 5; i++) {
             const starButton = document.createElement('button');
             starButton.type = 'button';
              starButton.className = 'focus:outline-none';
              starButton.innerHTML = `<i data-lucide="star" class="w-8 h-8 star-icon"></i>`;

              // Add event listeners
              starButton.addEventListener('mouseenter', () => updateStars(i, true));
             starButton.addEventListener('mouseleave', () => updateStars(currentRating, false));
              starButton.addEventListener('click', () => handleStarClick(i));
              starsContainer.appendChild(starButton);
         }
          // Initialize Lucide icons for stars
          lucide.createIcons();
          function updateStars(rating, isHover) {
             const stars = document.querySelectorAll('.star-icon');
               stars.forEach((star, index) => {
                  if (index < rating) {
                     star.setAttribute('fill', 'currentColor');
                      star.style.color = '#facc15';
                  } else {
                      star.setAttribute('fill', 'none');
                      star.style.color = '#6b7280';
                  }
              });
         }
          function handleStarClick(rating) {
             currentRating = rating;
             updateStars(rating, false);

             const additionalFields = document.getElementById('additionalFields');
              if (rating <= 3) {
                  additionalFields.classList.remove('hidden');
              } else {
                  window.location.href = googleReviewLink;
             }
          }
         document.getElementById('reviewForm').addEventListener('submit', async function(e) {
             e.preventDefault();
              const formData = {
                  rating: currentRating,
                  review: document.getElementById('review').value,
                  name: document.getElementById('name').value,
                 phone: document.getElementById('phone').value,
                  user_id: '{{ $user->id }}',
             };
             try{
                const response = await fetch('{{ route('reviews.store') }}',{
                     method: 'POST',
                     headers: {
                         'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                     },
                     body: JSON.stringify(formData)
                  });
                  const data = await response.json();
                  if(data.message){
                      alert(data.message);
                     window.location.href = '{{ route('dashboard') }}';
                  }
               }catch(error){
                    console.error('Error submitting review:', error);
               }
        });
    </script>
 </body>
 </html>