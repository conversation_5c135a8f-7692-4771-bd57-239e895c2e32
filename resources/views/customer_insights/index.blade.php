<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-2xl text-gray-800 leading-tight">
            Customer Insights Dashboard
        </h2>
    </x-slot>

    <div class="py-8 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Newest Customers Card -->
                <div class="bg-white rounded-xl shadow-lg p-6 transition-all duration-200 hover:shadow-xl border border-gray-100">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-800">Newest Customers</h3>
                        <a href="{{ route('customer_insights.export', 'newest_customers') }}" class="text-gray-500 hover:text-gray-700 transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                        </a>
                    </div>
                    <div class="mb-6">
                        <div class="flex items-center gap-3">
                            <label for="new_customer_days" class="text-sm font-medium text-gray-700">
                                Show customers added in last
                            </label>
                            <select id="new_customer_days" class="border rounded-lg px-4 py-2.5 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
                                <option value="15" @if(request('new_customer_days')==15) selected @endif>15 days</option>
                                <option value="30" @if(request('new_customer_days')==30) selected @endif>30 days</option>
                                <option value="60" @if(request('new_customer_days')==60) selected @endif>60 days</option>
                            </select>
                            <button onclick="window.location.href='?new_customer_days='+document.getElementById('new_customer_days').value"
                                    class="inline-flex items-center px-4 py-2.5 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-colors duration-200">
                                Apply
                            </button>
                        </div>
                    </div>
                    <div class="space-y-3 max-h-[400px] overflow-y-auto custom-scrollbar">
                        @forelse($newestCustomers as $customer)
                            <div class="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <span class="font-medium text-gray-900">{{ $customer->name }}</span>
                                        <p class="text-sm text-gray-500">{{ $customer->phone_number }}</p>
                                    </div>
                                    <span class="text-sm text-gray-600">
                                        Added {{ $customer->created_at->diffForHumans() }}
                                    </span>
                                </div>
                            </div>
                        @empty
                            <p class="text-gray-500 text-center py-4">No Customers Found</p>
                        @endforelse
                    </div>
                    <div class="mt-4">
                        {{ $newestCustomers->appends(request()->except('page'))->links() }}
                    </div>
                </div>

                <!-- Top Spending Customers Card -->
                <div class="bg-white rounded-xl shadow-lg p-6 transition-all duration-200 hover:shadow-xl border border-gray-100">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-800">Top Spending Customers</h3>
                        <a href="{{ route('customer_insights.export', 'top_customers') }}" class="text-gray-500 hover:text-gray-700 transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                        </a>
                    </div>
                    <div class="space-y-3">
                        @forelse($topCustomers as $customer)
                            <div class="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <span class="font-medium text-gray-900">{{ $customer->name }}</span>
                                        <p class="text-sm text-gray-500">{{ $customer->phone_number }}</p>
                                    </div>
                                    <span class="font-medium text-green-600">
                                        ₹{{ number_format($customer->purchases_sum_amount ?: 0, 2) }}
                                    </span>
                                </div>
                            </div>
                        @empty
                            <p class="text-gray-500 text-center py-4">No Customers Found</p>
                        @endforelse
                    </div>
                </div>

                <!-- Active vs Inactive Customers Chart -->
                <div class="bg-white rounded-xl shadow-lg p-6 transition-all duration-200 hover:shadow-xl border border-gray-100">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-800">Customer Activity</h3>
                        <a href="{{ route('customer_insights.export', 'active_inactive_customers') }}" class="text-gray-500 hover:text-gray-700 transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                        </a>
                    </div>
                    <div style="height: 300px; position: relative;">
                        <canvas id="customerActivityChart"></canvas>
                    </div>
                </div>

                <!-- Top Sales Dates Card -->
                <div class="bg-white rounded-xl shadow-lg p-6 transition-all duration-200 hover:shadow-xl border border-gray-100">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-800">Top Sales Dates</h3>
                        <a href="{{ route('customer_insights.export', 'top_sales_dates') }}" class="text-gray-500 hover:text-gray-700 transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                        </a>
                    </div>
                    <div class="space-y-3 max-h-[400px] overflow-y-auto custom-scrollbar">
                        @forelse($topSalesDates as $sale)
                            <div class="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                <div class="flex justify-between items-center">
                                    <span class="font-medium text-gray-900">{{ $sale->date }}</span>
                                    <span class="font-medium text-green-600">
                                        ₹{{ number_format($sale->total, 2) }}
                                    </span>
                                </div>
                            </div>
                        @empty
                            <p class="text-gray-500 text-center py-4">No Sales Found</p>
                        @endforelse
                    </div>
                </div>

                <!-- Tag-based Customers Card -->
                <div class="bg-white rounded-xl shadow-lg p-6 transition-all duration-200 hover:shadow-xl border border-gray-100">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-800">Customers by Tag</h3>
                    </div>
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Filter by Tags:</label>
                        <div class="flex flex-wrap gap-2">
                            @foreach($tags as $tag)
                                <div class="relative inline-block group">
                                    <input type="checkbox" name="selected_tags[]" value="{{ $tag->id }}" id="tag-{{ $tag->id }}"
                                           class="opacity-0 absolute left-0 top-0 w-full h-full z-10 cursor-pointer peer">
                                    <label for="tag-{{ $tag->id }}" class="bg-gray-200 rounded-full px-3 py-1 cursor-pointer text-sm peer-checked:bg-indigo-500 peer-checked:text-white transition-colors">
                                        {{ $tag->name }}
                                    </label>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    <div class="space-y-3 max-h-[400px] overflow-y-auto custom-scrollbar">
                        <ul id="tag_customer_list"></ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const activeCustomersCount = {{ $activeInactiveData['active'] }};
            const inactiveCustomersCount = {{ $activeInactiveData['inactive'] }};
            const tagCheckboxes = document.querySelectorAll('input[name="selected_tags[]"]');
            const customerTagList = document.getElementById('tag_customer_list');

            // Fetch and display customers by tag
            function fetchAndDisplayCustomers(selectedTags) {
                fetch('{{ route('customer_insights.get_customers_by_tag') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        tags: selectedTags
                    })
                })
                .then(response => response.json())
                .then(customers => {
                    customerTagList.innerHTML = '';
                    customers.forEach(customer => {
                        const li = document.createElement('li');
                        li.classList.add('p-4', 'bg-gray-50', 'rounded-lg', 'hover:bg-gray-100', 'transition-colors', 'flex', 'justify-between', 'items-center');
                        li.innerHTML = `
                            <div class="font-medium text-gray-900">
                                ${customer.name}
                                <p class="text-sm text-gray-500">${customer.phone_number}</p>
                            </div>
                            <span class="font-medium text-gray-600">
                                Loyalty Points: ${customer.loyalty_points?.points || 0}
                            </span>
                        `;
                        customerTagList.appendChild(li);
                    });
                })
                .catch(error => console.error('Error fetching customers:', error));
            }

            // Add event listeners to tag checkboxes
            tagCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const selectedTags = Array.from(tagCheckboxes)
                        .filter(checkbox => checkbox.checked)
                        .map(checkbox => checkbox.value);

                    fetchAndDisplayCustomers(selectedTags);
                });
            });

            // Initialize Customer Activity Chart
            const customerCtx = document.getElementById('customerActivityChart').getContext('2d');
            const customerChart = new Chart(customerCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Active Customers', 'Inactive Customers'],
                    datasets: [{
                        data: [activeCustomersCount, inactiveCustomersCount],
                        backgroundColor: [
                            'rgba(16, 185, 129, 0.8)', // Green for active
                            'rgba(239, 68, 68, 0.8)'   // Red for inactive
                        ],
                        borderColor: [
                            'rgba(16, 185, 129, 1)',
                            'rgba(239, 68, 68, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                font: {
                                    size: 12
                                }
                            }
                        }
                    },
                    layout: {
                        padding: 20
                    },
                    animation: {
                        animateScale: true,
                        animateRotate: true
                    }
                }
            });
        });
    </script>
</x-app-layout>