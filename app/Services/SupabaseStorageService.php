<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\UploadedFile;

class SupabaseStorageService
{
    private $url;
    private $serviceRoleKey;
    private $anonKey;
    private $bucketName;

    public function __construct()
    {
        $this->url = config('services.supabase.url');
        $this->serviceRoleKey = config('services.supabase.service_role_key');
        $this->anonKey = config('services.supabase.anon_key');
        $this->bucketName = config('services.supabase.bucket_name', 'cms-storage');
    }

    /**
     * Upload a file to Supabase storage
     */
    public function uploadFile(UploadedFile $file, string $path): array
    {
        try {
            // Ensure bucket exists first
            $this->createBucket();

            $fileName = $path . '/' . time() . '_' . $file->getClientOriginalName();
            $fileContent = file_get_contents($file->getPathname());
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->serviceRoleKey,
                'Content-Type' => $file->getMimeType(),
                'Content-Length' => strlen($fileContent),
            ])->withBody($fileContent, $file->getMimeType())
            ->put($this->url . '/storage/v1/object/' . $this->bucketName . '/' . $fileName);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'path' => $fileName,
                    'url' => $this->getPublicUrl($fileName),
                ];
            }

            Log::error('Supabase upload failed', [
                'response' => $response->body(),
                'status' => $response->status(),
                'fileName' => $fileName
            ]);
            return ['success' => false, 'error' => 'Upload failed: ' . $response->body()];

        } catch (\Exception $e) {
            Log::error('Supabase upload exception', ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Upload file content directly
     */
    public function uploadFileContent(string $content, string $fileName, string $mimeType = 'image/png'): array
    {
        try {
            // Ensure bucket exists first
            $this->createBucket();

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->serviceRoleKey,
                'Content-Type' => $mimeType,
                'Content-Length' => strlen($content),
            ])->withBody($content, $mimeType)
            ->put($this->url . '/storage/v1/object/' . $this->bucketName . '/' . $fileName);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'path' => $fileName,
                    'url' => $this->getPublicUrl($fileName),
                ];
            }

            Log::error('Supabase content upload failed', [
                'response' => $response->body(),
                'status' => $response->status(),
                'fileName' => $fileName
            ]);
            return ['success' => false, 'error' => 'Upload failed: ' . $response->body()];

        } catch (\Exception $e) {
            Log::error('Supabase content upload exception', ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Delete a file from Supabase storage
     */
    public function deleteFile(string $path): bool
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->serviceRoleKey,
            ])->delete(
                $this->url . '/storage/v1/object/' . $this->bucketName . '/' . $path
            );

            return $response->successful();

        } catch (\Exception $e) {
            Log::error('Supabase delete exception', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Get public URL for a file
     */
    public function getPublicUrl(string $path): string
    {
        return $this->url . '/storage/v1/object/public/' . $this->bucketName . '/' . $path;
    }

    /**
     * Create bucket if it doesn't exist
     */
    public function createBucket(): array
    {
        try {
            // First check if bucket exists
            $checkResponse = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->serviceRoleKey,
            ])->get($this->url . '/storage/v1/bucket/' . $this->bucketName);

            if ($checkResponse->successful()) {
                return ['success' => true, 'message' => 'Bucket already exists'];
            }

            // Create bucket if it doesn't exist
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->serviceRoleKey,
                'Content-Type' => 'application/json',
            ])->post(
                $this->url . '/storage/v1/bucket',
                [
                    'id' => $this->bucketName,
                    'name' => $this->bucketName,
                    'public' => true,
                    'file_size_limit' => null,
                    'allowed_mime_types' => null,
                ]
            );

            if ($response->successful()) {
                return ['success' => true, 'message' => 'Bucket created successfully'];
            }

            // If bucket already exists, that's fine
            if ($response->status() === 409) {
                return ['success' => true, 'message' => 'Bucket already exists'];
            }

            Log::error('Supabase bucket creation failed', [
                'response' => $response->body(),
                'status' => $response->status()
            ]);
            return ['success' => false, 'error' => $response->body()];

        } catch (\Exception $e) {
            Log::error('Supabase bucket creation exception', ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
