<?php

namespace App\Console\Commands;

use App\Services\SupabaseStorageService;
use Illuminate\Console\Command;

class InitializeSupabaseBucket extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'supabase:init-bucket';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Initialize Supabase storage bucket for CMS';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Initializing Supabase storage bucket...');

        $supabaseService = new SupabaseStorageService();
        $result = $supabaseService->createBucket();

        if ($result['success']) {
            $this->info('✅ ' . $result['message']);
        } else {
            $this->error('❌ Failed to create bucket: ' . $result['error']);
            return 1;
        }

        return 0;
    }
}
