<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Tag extends Model
{
    use HasFactory;

    protected $fillable = [
         'user_id',
        'name',
    ];
     public function user(): BelongsTo
     {
         return $this->belongsTo(User::class);
     }
    public function customers(): BelongsToMany
    {
        return $this->belongsToMany(Customer::class);
    }
}