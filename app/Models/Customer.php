<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Customer extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'phone_number',
        'email',
        'date_of_birth',
         'anniversary_date',
    ];

   public function user(): BelongsTo
   {
       return $this->belongsTo(User::class);
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class);
    }
   public function customFieldValues(): HasMany
   {
       return $this->hasMany(CustomerCustomFieldValue::class);
   }
   public function purchases(): HasMany
   {
       return $this->hasMany(Purchase::class);
    }
   public function loyaltyPoints(): HasOne
    {
        return $this->hasOne(CustomerLoyaltyPoints::class);
    }
}