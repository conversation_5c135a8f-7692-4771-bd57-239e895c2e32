<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Template extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'layout',
        'content',
        'content_image',
        'content_image_supabase_url',
        'inner_image_supabase_url',
        'background_color',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the template content URL from Supabase
     */
    public function getContentUrl(): ?string
    {
        return $this->content_image_supabase_url;
    }

    /**
     * Get a scaled-down preview version of the template content
     */
    public function getPreviewHtml(): string
    {
        // If we have a Supabase preview image, use that instead
        if ($this->content_image_supabase_url) {
            return '<img src="' . $this->content_image_supabase_url . '" alt="Template Preview" class="w-full h-full object-cover rounded-lg" />';
        }

        $content = $this->content;

        // Add preview-specific styles
        $previewStyles = '
            <style>
                .preview-wrapper {
                    transform: scale(0.5);
                    transform-origin: top left;
                    width: 200%;
                    height: 200%;
                    overflow: hidden;
                }
                .preview-container {
                    width: 50%;
                    height: 50%;
                    overflow: hidden;
                }
            </style>
        ';

        // Insert preview styles before closing head tag
        $content = str_replace('</head>', $previewStyles . '</head>', $content);

        // Wrap content in preview container
        return '
            <div class="preview-container">
                <div class="preview-wrapper">
                    ' . $content . '
                </div>
            </div>
        ';
    }

    /**
     * Get the display content with Supabase URLs instead of base64 images
     */
    public function getDisplayContent(): string
    {
        $content = $this->content;

        // If we have a stored Supabase URL for the inner image, replace base64 with it
        if ($this->inner_image_supabase_url) {
            // Replace base64 image data with Supabase URL
            $content = preg_replace(
                '/src="data:image\/[^;]+;base64,[^"]*"/',
                "src=\"{$this->inner_image_supabase_url}\"",
                $content
            );
        }

        return $content;
    }
}
