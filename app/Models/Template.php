<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Template extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'layout',
        'content',
        'content_image',
        'content_image_supabase_url',
        'background_color',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the template content URL from Supabase
     */
    public function getContentUrl(): ?string
    {
        return $this->content_image_supabase_url;
    }

    /**
     * Get a scaled-down preview version of the template content
     */
    public function getPreviewHtml(): string
    {
        $content = $this->content;
        
        // Add preview-specific styles
        $previewStyles = '
            <style>
                .preview-wrapper {
                    transform: scale(0.5);
                    transform-origin: top left;
                    width: 200%;
                    height: 200%;
                    overflow: hidden;
                }
                .preview-container {
                    width: 50%;
                    height: 50%;
                    overflow: hidden;
                }
            </style>
        ';
        
        // Insert preview styles before closing head tag
        $content = str_replace('</head>', $previewStyles . '</head>', $content);
        
        // Wrap content in preview container
        return '
            <div class="preview-container">
                <div class="preview-wrapper">
                    ' . $content . '
                </div>
            </div>
        ';
    }
}
