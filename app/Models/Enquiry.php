<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Enquiry extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'phone_number',
        'lead_type',
        'additional_notes',
        'marketing_team_member_id', // Add this
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function responses(): HasMany
    {
        return $this->hasMany(EnquiryResponse::class);
    }

    public function marketingTeamMember(): BelongsTo // Add this relationship
    {
        return $this->belongsTo(MarketingTeamMember::class);
    }
}