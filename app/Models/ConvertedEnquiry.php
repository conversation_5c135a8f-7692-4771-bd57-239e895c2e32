<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ConvertedEnquiry extends Model
{
    use HasFactory;
     protected $fillable = [
          'user_id',
          'enquiry_id',
          'customer_id'
     ];
     public function user(): BelongsTo
      {
          return $this->belongsTo(User::class);
      }
      public function enquiry(): BelongsTo
     {
         return $this->belongsTo(Enquiry::class);
      }
      public function customer(): BelongsTo
      {
         return $this->belongsTo(Customer::class);
      }

}