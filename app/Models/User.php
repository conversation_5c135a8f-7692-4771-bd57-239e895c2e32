<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    public function customers()
    {
        return $this->hasMany(Customer::class);
    }

    public function purchases()
    {
        return $this->hasMany(Purchase::class);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'company_name',
        'google_review_link',
        'phone_number',
        'purchase_message',
         'birthday_message',
        'anniversary_message',
        'frontdesk_username',
        'frontdesk_password',
        'api_key',
        'trial_ends_at',
        'subscription_ends_at',
        'is_active',
        'subscription_status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'frontdesk_password',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'frontdesk_password' => 'hashed',
            'trial_ends_at' => 'datetime',
            'subscription_ends_at' => 'datetime',
        ];
    }

    /**
     * Check if user's subscription is active
     */
    public function hasActiveSubscription(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now();
        
        // Check trial period
        if ($this->subscription_status === 'trial') {
            return $this->trial_ends_at && $this->trial_ends_at->isFuture();
        }
        
        // Check active subscription
        if ($this->subscription_status === 'active') {
            return $this->subscription_ends_at && $this->subscription_ends_at->isFuture();
        }
        
        return false;
    }

    /**
     * Get days remaining in subscription
     */
    public function getDaysRemaining(): int
    {
        $now = now();
        
        if ($this->subscription_status === 'trial' && $this->trial_ends_at) {
            return max(0, $now->diffInDays($this->trial_ends_at, false));
        }
        
        if ($this->subscription_status === 'active' && $this->subscription_ends_at) {
            return max(0, $now->diffInDays($this->subscription_ends_at, false));
        }
        
        return 0;
    }

    /**
     * Update subscription status based on current date
     */
    public function updateSubscriptionStatus(): void
    {
        $now = now();
        
        if ($this->subscription_status === 'trial' && $this->trial_ends_at && $this->trial_ends_at->isPast()) {
            $this->update(['subscription_status' => 'expired']);
        }
        
        if ($this->subscription_status === 'active' && $this->subscription_ends_at && $this->subscription_ends_at->isPast()) {
            $this->update(['subscription_status' => 'expired']);
        }
    }

    /**
     * Boot method to set trial period for new users
     */
    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($user) {
            if (!$user->trial_ends_at) {
                $user->trial_ends_at = now()->addDays(15);
                $user->subscription_status = 'trial';
                $user->is_active = true;
            }
        });
    }
}