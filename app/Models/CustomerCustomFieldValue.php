<?php

    namespace App\Models;

    use Illuminate\Database\Eloquent\Factories\HasFactory;
    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Database\Eloquent\Relations\BelongsTo;

    class CustomerCustomFieldValue extends Model
    {
        use HasFactory;

        protected $fillable = [
            'customer_id',
            'custom_field_id',
            'value'
        ];

        public function customer(): BelongsTo
        {
            return $this->belongsTo(Customer::class);
        }
        public function customField(): BelongsTo
        {
            return $this->belongsTo(CustomField::class);
        }
    }