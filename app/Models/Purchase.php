<?php

namespace App\Models;

 use Illuminate\Database\Eloquent\Factories\HasFactory;
 use Illuminate\Database\Eloquent\Model;
 use Illuminate\Database\Eloquent\Relations\BelongsTo;

 class Purchase extends Model
 {
     use HasFactory;

     protected $fillable = [
         'user_id',
         'customer_id',
         'amount',
         'discount',
         'loyalty_points_spent',
     ];
        public function user(): BelongsTo
        {
            return $this->belongsTo(User::class);
        }
     public function customer(): BelongsTo
     {
         return $this->belongsTo(Customer::class);
     }
 }