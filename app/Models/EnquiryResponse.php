<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EnquiryResponse extends Model
{
    use HasFactory;

    protected $fillable = [
        'enquiry_id',
        'enquiry_question_id',
        'response',
    ];

    public function enquiry(): BelongsTo
    {
        return $this->belongsTo(Enquiry::class);
    }

    public function question(): BelongsTo
    {
        return $this->belongsTo(EnquiryQuestion::class, 'enquiry_question_id');
    }
}