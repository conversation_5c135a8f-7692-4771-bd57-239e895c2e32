<?php

      namespace App\Jobs;

      use App\Models\Customer;
      use App\Models\User;
      use Illuminate\Bus\Queueable;
      use Illuminate\Contracts\Queue\ShouldBeUnique;
      use Illuminate\Contracts\Queue\ShouldQueue;
      use Illuminate\Foundation\Bus\Dispatchable;
      use Illuminate\Queue\InteractsWithQueue;
      use Illuminate\Queue\SerializesModels;
      use Illuminate\Support\Facades\Http;

      class SendBirthdayAndAnniversaryMessages implements ShouldQueue
      {
          use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

          /**
           * Create a new job instance.
           */
          public function __construct()
          {
              //
          }

          /**
          * Execute the job.
           */
          public function handle(): void
          {
              $users = User::all();
              foreach($users as $user){
                 $birthdayCustomers = CustomerController::getBirthdayCustomers($user->id);
                  $anniversaryCustomers = CustomerController::getAnniversaryCustomers($user->id);
                  foreach($birthdayCustomers as $customer){
                      $message = $user->birthday_message ?? "Happy Birthday {$customer->name}, We wish you all the best on your special day";
                       $this->sendWhatsappMessage($customer->phone_number, $message);
                   }
                  foreach($anniversaryCustomers as $customer){
                      $message = $user->anniversary_message ?? "Happy Anniversary {$customer->name}, wishing you all the best on this special occasion";
                      $this->sendWhatsappMessage($customer->phone_number, $message);
                  }
              }
          }
         private function sendWhatsappMessage($phoneNumber, $message)
          {
              $response = Http::post('https://wa.connectezee.com/api/send', [
                 "number" => $phoneNumber,
                 "type" => "text",
                 "message" => $message,
                  "instance_id" => "66E68C43737FD",
                  "access_token" => "66b1ad7d11db0"
               ]);
            //  return $response->json();
          }
      }