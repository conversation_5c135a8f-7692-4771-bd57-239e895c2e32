<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
     <meta charset="utf-8">
     <meta name="viewport" content="width=device-width, initial-scale=1">
     <meta name="csrf-token" content="{{ csrf_token() }}">

     <title>{{ config('app.name', 'Laravel') }} - Front Desk</title>

     <!-- Fonts -->
     <link rel="preconnect" href="https://fonts.bunny.net">
     <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

     <!-- Scripts -->
     @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased">
    <div class="min-h-screen bg-gray-100 flex flex-col">
       <!-- Top Bar -->
        <nav class="bg-white border-b border-gray-200 py-2">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                         <a href="{{ route('frontdesk.dashboard') }}" class="text-lg font-semibold text-gray-800">
                             Front Desk
                        </a>
                    </div>
                     <a href="{{ route('frontdesk.dashboard') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Back to Dashboard
                     </a>
                 </div>
            </div>
        </nav>
        <!-- Page Content -->
         <main class="flex-grow p-6">
             @if (session('success'))
                <div class="rounded-lg bg-green-50 p-4 border border-green-200 mb-6">
                     <div class="flex">
                         <div class="flex-shrink-0">
                             <svg class="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                             </svg>
                         </div>
                        <div class="ml-3">
                             <h3 class="text-sm font-medium text-green-800">Success</h3>
                             <div class="mt-2 text-sm text-green-700">
                                 <p>@yield('success')</p>
                             </div>
                         </div>
                     </div>
                </div>
             @endif

             @if ($errors->any())
                 <div class="rounded-lg bg-red-50 p-4 border border-red-200 mb-6">
                     <div class="flex">
                         <div class="flex-shrink-0">
                             <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                             </svg>
                         </div>
                         <div class="ml-3">
                             <h3 class="text-sm font-medium text-red-800">Error</h3>
                             <div class="mt-2 text-sm text-red-700">
                                 <ul>
                                     @foreach ($errors->all() as $error)
                                         <li>{{ $error }}</li>
                                     @endforeach
                                 </ul>
                             </div>
                         </div>
                     </div>
                 </div>
             @endif
             @yield('content')
         </main>
     </div>
 </body>
</html>