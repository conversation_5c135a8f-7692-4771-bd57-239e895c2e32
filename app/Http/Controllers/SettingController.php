<?php

 namespace App\Http\Controllers;

 use Illuminate\Http\Request;
 use Illuminate\Support\Facades\Auth;
 use Illuminate\Support\Facades\Hash;

 class SettingController extends Controller
 {
      public function index()
      {
          return view('settings.index');
      }

      public function updateCompanyDetails(Request $request)
     {
          $request->validate([
              'company_name' => 'required|string',
               'google_review_link' => 'nullable|url',
         ]);
          $user = Auth::user();
          $user->company_name = $request->company_name;
          $user->google_review_link = $request->google_review_link;
          $user->save();

          return redirect()->route('settings.index')->with('success', 'Company details updated successfully.');
     }
     public function updatePassword(Request $request)
      {
          $request->validate([
               'current_password' => 'required|string',
              'new_password' => 'required|string|min:8|confirmed',
         ]);

         $user = Auth::user();
          if (!Hash::check($request->current_password, $user->password)) {
              return redirect()->back()->withErrors(['current_password' => 'Current password does not match.']);
          }

          $user->password = Hash::make($request->new_password);
          $user->save();

          return redirect()->route('settings.index')->with('success', 'Password updated successfully.');
     }
      public function updateMessage(Request $request)
      {
         $request->validate([
             'purchase_message' => 'nullable|string',
             'birthday_message' => 'nullable|string',
              'anniversary_message' => 'nullable|string',
          ]);

         $user = Auth::user();
          $user->purchase_message = $request->purchase_message;
           $user->birthday_message = $request->birthday_message;
          $user->anniversary_message = $request->anniversary_message;
         $user->save();
         return redirect()->route('settings.index')->with('success', 'Messages updated successfully.');
      }
     public function updatePhoneNumber(Request $request)
      {
          $request->validate([
             'phone_number' => 'required|string'
          ]);
         $user = Auth::user();
         $user->phone_number = $request->phone_number;
         $user->save();

          return redirect()->route('settings.index')->with('success', 'Phone Number updated successfully.');
     }
  }