<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Tag;
use App\Models\Template; // Import the Template model
use App\Services\SupabaseStorageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CampaignController extends Controller
{
    private $instanceId = "66E68C43737FD";

    public function index()
    {
       $tags = Tag::where('user_id', Auth::id())->get();
        $customers = Customer::where('user_id', Auth::id())->get(); // Fetch all customers initially
        $templates = Template::where('user_id', Auth::id())->get(); // Fetch templates

        return view('campaigns.index', compact('tags', 'customers', 'templates'));
    }
    public function sendCampaign(Request $request)
{
    // Validate common fields
   $request->validate([
        'customer_ids' => 'required',
        'message' => 'nullable|string', // Message can be optional if a template is selected
       'media' => 'nullable|file|mimes:jpeg,png,gif,mp4,avi,mov,pdf|max:5120',
        'template_id' => 'nullable|exists:templates,id', // Validate template ID
    ]);
     $customerIds = $request->customer_ids;
    if (is_string($customerIds)) {
       $customerIds = json_decode($customerIds, true);
   }
      $request->merge(['customer_ids' => $customerIds]);
     $request->validate([
       'customer_ids' => 'required|array|min:1',
    ]);
    $customers = Customer::where('user_id', Auth::id())->whereIn('id', $request->customer_ids)->get();

    // Handle template sending
   if ($request->filled('template_id')) {
      $template = Template::findOrFail($request->template_id);
        // Check if the template belongs to the current user
        if ($template->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.'); // Or handle it as you see fit
        }

      foreach ($customers as $customer) {
          $this->sendWhatsappTemplateMessage($customer->phone_number, $template);
        }
        return redirect()->route('campaigns.index')->with('success', 'Campaign sent using template.');
    }

    $supabaseService = new SupabaseStorageService();
   $mediaUrl = null;
    $fileName = null;
   if($request->hasFile('media')){
         $media = $request->file('media');
         $fileName = Str::uuid() . '_' . $media->getClientOriginalName();
         
         // Upload to Supabase first
         $supabaseResult = $supabaseService->uploadFile($media, 'campaign-media');
         if ($supabaseResult['success']) {
             $mediaUrl = $supabaseResult['url'];
         } else {
             // Fallback to local storage
             $path = $media->storeAs('campaign-media', $fileName, 'public');
             $mediaUrl = Storage::url($path);
         }
    }

   foreach($customers as $customer){
        if($mediaUrl){
            $this->sendWhatsappMediaMessage($customer->phone_number, $request->message, $mediaUrl, $fileName);
        }else{
           $this->sendWhatsappTextMessage($customer->phone_number, $request->message);
        }
    }

   return redirect()->route('campaigns.index')->with('success', 'Campaign sent to selected customers.');
}
   public function getFilteredCustomers(Request $request)
    {
        $query = Customer::where('user_id', Auth::id());

         if ($request->has('tag_id') && $request->tag_id != 'all') {
            $query->whereHas('tags', function($q) use ($request){
                $q->where('tag_id', $request->tag_id);
           });
       }
        if ($request->has('date_filter')) {
             if($request->date_filter == 'old'){
                 $query->orderBy('created_at', 'asc');
             } else if ($request->date_filter == 'new'){
                $query->orderBy('created_at', 'desc');
             }
        }
        if ($request->has('spending_filter')) {
             $customersWithSpendings = Customer::withSum('purchases','amount')
                 ->orderBy('purchases_sum_amount', $request->spending_filter)
                ->pluck('id')
                ->toArray();
           $query->whereIn('id', $customersWithSpendings);
       }

       if ($request->has('activity_filter')) {
            if($request->activity_filter == 'active'){
               $activeCustomers = Customer::where('user_id', Auth::id())
                     ->whereHas('purchases', function($q) {
                        $q->where('created_at', '>=', now()->subDays(30));
                     })
                     ->pluck('id')
                     ->toArray();
                $query->whereIn('id', $activeCustomers);
            } else if ($request->activity_filter == 'inactive'){
                $inactiveCustomers = Customer::where('user_id', Auth::id())
                     ->where(function($q){
                        $q->whereDoesntHave('purchases')
                              ->orWhereHas('purchases', function($q) {
                                   $q->where('created_at', '<', now()->subDays(30));
                              });
                    })
                     ->pluck('id')
                    ->toArray();
               $query->whereIn('id', $inactiveCustomers);
            }
        }

        $customers = $query->get();
        return response()->json($customers);
    }
    private function sendWhatsappTextMessage($phoneNumber, $message)
    {
        $appkey = config('services.whatsapp.appkey');
        $authkey = config('services.whatsapp.authkey');

        $response = Http::asMultipart()->post('https://waba.connectezee.com/api/create-message', [
            'appkey' => $appkey,
            'authkey' => $authkey,
            'to' => $phoneNumber,
            'message' => $message,
        ]);
        // return $response->json();
    }

    private function sendWhatsappMediaMessage($phoneNumber, $message, $mediaUrl, $fileName)
    {
        $appkey = config('services.whatsapp.appkey');
        $authkey = config('services.whatsapp.authkey');

        $response = Http::asMultipart()->post('https://waba.connectezee.com/api/create-message', [
            'appkey' => $appkey,
            'authkey' => $authkey,
            'to' => $phoneNumber,
            'message' => $message,
            'file' => $mediaUrl,
        ]);
        // return $response->json();
    }

    private function sendWhatsappTemplateMessage($phoneNumber, $template)
    {
        $appkey = config('services.whatsapp.appkey');
        $authkey = config('services.whatsapp.authkey');

        $templateId = $template->template_id; // Assuming template model has template_id field
        $variables = $template->getTemplateVariables(); // Assuming method to get variables as associative array

        $postData = [
            'appkey' => $appkey,
            'authkey' => $authkey,
            'to' => $phoneNumber,
            'template_id' => $templateId,
        ];

        if (!empty($variables)) {
            foreach ($variables as $key => $value) {
                $postData["variables[{$key}]"] = $value;
            }
        }

        $response = Http::asMultipart()->post('https://waba.connectezee.com/api/create-message', $postData);
        // return $response->json();
    }

}