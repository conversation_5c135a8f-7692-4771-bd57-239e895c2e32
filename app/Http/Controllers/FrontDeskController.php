<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\EnquiryQuestion;
use App\Models\Purchase;
use App\Models\Tag;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class FrontDeskController extends Controller
{
      public function configureCredentials()
       {
          return view('frontdesk.configure');
       }
       public function storeCredentials(Request $request)
       {
          $request->validate([
               'frontdesk_username' => 'required|string|unique:users,frontdesk_username,'.Auth::id(),
               'frontdesk_password' => 'required|string|min:6',
          ]);

          $user = Auth::user();
           $user->frontdesk_username = $request->frontdesk_username;
          $user->frontdesk_password = Hash::make($request->frontdesk_password);
           $user->save();

           return redirect()->route('frontdesk.configure')->with('success', 'Front Desk credentials saved successfully.');
       }
       public function frontdeskLogin(Request $request)
       {
           $credentials = $request->validate([
              'frontdesk_username' => 'required|string',
               'frontdesk_password' => 'required|string',
           ]);
          $user = User::where('frontdesk_username', $credentials['frontdesk_username'])->first();

           if ($user && Hash::check($credentials['frontdesk_password'], $user->frontdesk_password)) {
              Auth::login($user);
               return redirect()->route('frontdesk.dashboard');
           }
          return back()->withErrors(['frontdesk_username' => 'Invalid credentials.']);
       }

      public function showLoginForm()
      {
          return view('frontdesk.login');
       }
       public function dashboard()
       {
         if(!Auth::check()){
              return redirect()->route('frontdesk.login.show');
          }
          $tags = Tag::where('user_id', Auth::id())->get();
          return view('frontdesk.dashboard', compact('tags'));
      }
      public function accounting()
      {
          $tags = Tag::where('user_id', Auth::id())->get();
          return view('frontdesk.accounting', compact('tags'));
      }
      public function enquiry()
      {
          $questions = EnquiryQuestion::where('user_id', Auth::id())->orderBy('order')->get();
          return view('frontdesk.enquiry', compact('questions'));
      }

   }
  