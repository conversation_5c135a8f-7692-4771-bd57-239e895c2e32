<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\CustomerLoyaltyPoints;
use App\Models\LoyaltyConfiguration;
use App\Models\Purchase;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;

class PurchaseController extends Controller
{
      public function index()
      {
         $purchases = Purchase::where('user_id', Auth::id())->latest()->paginate(10);
          return view('purchases.index', compact('purchases'));
      }

     public function create()
     {
        $tags = Tag::where('user_id', Auth::id())->get();
         return view('purchases.create', compact('tags'));
      }
     public function store(Request $request)
      {
          $request->validate([
              'customer_id' => 'required|exists:customers,id,user_id,'.Auth::id(),
               'amount' => 'required|numeric|min:0',
               'discount' => 'nullable|numeric|min:0',
               'loyalty_points_spent' => 'nullable|integer|min:0',
          ]);
          $customer = Customer::find($request->customer_id);
          $loyaltyConfig = LoyaltyConfiguration::where('user_id', Auth::id())->first();
         $loyaltyPoints = CustomerLoyaltyPoints::where('customer_id', $customer->id)->first();

          $totalPoints = 0;
         $purchaseAmount = $request->amount - ($request->discount ?? 0);
          if($loyaltyConfig){
              $totalPoints =  floor($purchaseAmount * $loyaltyConfig->earning_rate);
          }

          $purchase = Purchase::create([
               'user_id' => Auth::id(),
               'customer_id' => $request->customer_id,
               'amount' => $request->amount,
               'discount' => $request->discount ?? 0,
               'loyalty_points_spent' => $request->loyalty_points_spent ?? 0,
          ]);
          if($loyaltyPoints){
             $loyaltyPoints->update([
                  'points' => $loyaltyPoints->points + $totalPoints - ($request->loyalty_points_spent ?? 0)
              ]);
          }else{
              CustomerLoyaltyPoints::create([
                 'customer_id' => $customer->id,
                  'points' => $totalPoints - ($request->loyalty_points_spent ?? 0)
              ]);
          }
         // Send WhatsApp message (replace with your actual logic)
         $this->sendWhatsappMessage($customer->phone_number, Auth::user()->company_name, Auth::user()->purchase_message, Auth::id());

          return redirect()->route('purchases.index')->with('success', 'Purchase registered successfully.');
     }
     public function searchCustomer(Request $request)
     {
        $search = $request->search;
          $customers = Customer::where('user_id', Auth::id())
            ->with('loyaltyPoints')
             ->where(function ($query) use ($search) {
                  $query->where('name', 'like', '%'.$search.'%')
                       ->orWhere('phone_number', 'like', '%'.$search.'%');
             })
            ->get();
          return response()->json($customers);
     }
     private function sendWhatsappMessage($phoneNumber, $companyName, $purchaseMessage, $userId)
    {
        $reviewLink = route('reviews.review_page', $userId);
         $message = str_replace('{link}', $reviewLink, $purchaseMessage);
          $message = $message ? $message : "Thank you for visiting {$companyName} hope to see you again";
        $response = Http::post('https://wa.connectezee.com/api/send', [
            "number" => $phoneNumber,
             "type" => "text",
            "message" => $message,
             "instance_id" => "66E68C43737FD",
            "access_token" => "66b1ad7d11db0"
        ]);
        // return $response->json();
   }
   public function createCustomer(Request $request)
   {
        $request->validate([
            'name' => 'required|string',
            'phone_number' => 'required|string|unique:customers,phone_number,NULL,id,user_id,' . Auth::id(),
            'email' => 'nullable|email',
        ]);

        $customer = Customer::create([
            'user_id' => Auth::id(),
             'name' => $request->name,
            'phone_number' => $request->phone_number,
            'email' => $request->email,
        ]);

        return response()->json($customer);
    }
}