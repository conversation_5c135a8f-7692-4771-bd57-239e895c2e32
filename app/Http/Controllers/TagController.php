<?php

namespace App\Http\Controllers;

use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TagController extends Controller
{
     public function index()
    {
        $tags = Tag::where('user_id', Auth::id())->latest()->paginate(10);
         return view('tags.index', compact('tags'));
    }

     public function create()
    {
        return view('tags.create');
    }
    public function store(Request $request)
    {
         $request->validate([
             'name' => 'required|string|unique:tags,name,NULL,id,user_id,' . Auth::id(),
         ]);

       Tag::create([
        'user_id' => Auth::id(),
        'name' => $request->name,
       ]);

        return redirect()->route('tags.index')->with('success', 'Tag created successfully.');
    }

    public function edit(Tag $tag)
   {
       if ($tag->user_id !== Auth::id()) {
           abort(403); // Unauthorized access
       }
        return view('tags.edit', compact('tag'));
   }
   public function update(Request $request, Tag $tag)
    {
       if ($tag->user_id !== Auth::id()) {
           abort(403); // Unauthorized access
       }
         $request->validate([
            'name' => 'required|string|unique:tags,name,'.$tag->id.',id,user_id,' . Auth::id(),
         ]);


      $tag->update([
          'name' => $request->name
      ]);


       return redirect()->route('tags.index')->with('success', 'Tag updated successfully.');
   }

    public function destroy(Tag $tag)
   {
       if ($tag->user_id !== Auth::id()) {
           abort(403); // Unauthorized access
       }
       $tag->delete();
        return redirect()->route('tags.index')->with('success', 'Tag deleted successfully.');
   }
}