<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Purchase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\StreamedResponse;
use App\Models\Tag;

class CustomerInsightController extends Controller
{
   public function index(Request $request)
    {
         $userId = Auth::id();
       $newestCustomers = $this->getNewestCustomers($userId, $request->input('new_customer_days', 15));
        $topCustomers = $this->getTopSpendingCustomers($userId, 10);
         $activeInactiveData = $this->getActiveInactiveCustomers($userId, $request->input('active_inactive_per_page', 10));
        $topSalesDates = $this->getTopSalesDates($userId, $request->input('top_sales_per_page', 10));
         $tags = Tag::where('user_id', $userId)->get();

       return view('customer_insights.index', compact('newestCustomers', 'topCustomers', 'activeInactiveData','topSalesDates', 'tags'));

   }
   private function getNewestCustomers($userId, $days = 15)
    {
      return Customer::where('user_id', $userId)
           ->where('created_at', '>=', now()->subDays($days))
           ->orderBy('created_at', 'desc')
            ->paginate(10);
   }
   private function getTopSpendingCustomers($userId, $limit = 10)
    {
        return Customer::where('user_id', $userId)
            ->withSum('purchases', 'amount')
           ->orderBy('purchases_sum_amount', 'desc')
            ->take($limit)
            ->get();
   }
   private function getActiveInactiveCustomers($userId, $perPage = 10)
   {
        $activeCustomers = Customer::where('user_id', $userId)
            ->whereHas('purchases', function($query) {
               $query->where('created_at', '>=', now()->subDays(30));
            })
             ->count();
       $inactiveCustomers = Customer::where('user_id', $userId)
            ->where(function($query){
               $query->whereDoesntHave('purchases')
                     ->orWhereHas('purchases', function($query) {
                       $query->where('created_at', '<', now()->subDays(30));
                    });
           })
          ->count();
        return [
           'active' => $activeCustomers,
            'inactive' => $inactiveCustomers,
        ];
   }
    private function getTopSalesDates($userId, $limit = 10)
    {
         return Purchase::where('user_id', $userId)
             ->select(DB::raw('DATE(created_at) as date'), DB::raw('SUM(amount) as total'))
            ->groupBy('date')
           ->orderBy('total', 'desc')
            ->take($limit)
            ->get();
   }
    public function getCustomersByTag(Request $request)
    {
        $request->validate([
           'tags' => 'nullable|array'
       ]);

        $query = Customer::where('user_id', Auth::id())
            ->with('loyaltyPoints');
        if($request->has('tags') && !empty($request->tags))
           {
                $query->whereHas('tags', function($q) use ($request){
                   $q->whereIn('tag_id', $request->tags);
                });
            }

       $customers = $query->get();
        return response()->json($customers);
    }
   public function export(Request $request, $type)
   {
       $userId = Auth::id();
        $filename = 'customer_insights_' . $type . '_' . now()->format('Ymd_His') . '.csv';
       $headers = [
           'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

       $callback = function () use ($userId, $type) {
           $output = fopen('php://output', 'w');
           fputcsv($output, $this->getExportHeaders($type));
           $this->getExportData($userId, $type, $output);

            fclose($output);
        };

       return new StreamedResponse($callback, 200, $headers);
   }
   private function getExportHeaders($type)
    {
       switch($type){
        case 'newest_customers':
            return ['Customer ID', 'Name', 'Phone', 'Email', 'Created At'];
        case 'top_customers':
           return ['Customer ID', 'Name', 'Phone', 'Email', 'Total Spent'];
       case 'active_inactive_customers':
            return ['Type', 'Count'];
       case 'top_sales_dates':
             return ['Date', 'Total Sales'];
       default:
            return [];
       }
   }
  private function getExportData($userId, $type, $output)
    {
        switch($type) {
            case 'newest_customers':
                $customers = Customer::where('user_id', $userId)
                    ->orderBy('created_at', 'desc')
                    ->get();
              foreach($customers as $customer){
                    fputcsv($output, [
                        $customer->id,
                        $customer->name,
                       $customer->phone_number,
                       $customer->email,
                        $customer->created_at->format('Y-m-d H:i:s')
                    ]);
                }
                break;
            case 'top_customers':
                $customers = Customer::where('user_id', $userId)
                   ->withSum('purchases', 'amount')
                    ->orderBy('purchases_sum_amount', 'desc')
                    ->get();
                foreach($customers as $customer){
                    fputcsv($output, [
                        $customer->id,
                        $customer->name,
                        $customer->phone_number,
                       $customer->email,
                        $customer->purchases_sum_amount,
                    ]);
                }
                break;
            case 'active_inactive_customers':
              $activeInactive =  $this->getActiveInactiveCustomers($userId);
                  fputcsv($output, ['Active', $activeInactive['active']]);
                   fputcsv($output, ['Inactive', $activeInactive['inactive']]);
              break;
            case 'top_sales_dates':
                 $sales = Purchase::where('user_id', $userId)
                     ->select(DB::raw('DATE(created_at) as date'), DB::raw('SUM(amount) as total'))
                    ->groupBy('date')
                    ->orderBy('total', 'desc')
                     ->get();

                foreach($sales as $sale){
                     fputcsv($output, [
                         $sale->date,
                        $sale->total,
                    ]);
                }
               break;
       }
  }
}