<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SubscriptionController extends Controller
{
    public function expired()
    {
        $user = Auth::user();
        
        if ($user) {
            // Update subscription status first
            $user->updateSubscriptionStatus();
            
            // If user now has active subscription, redirect to dashboard
            if ($user->hasActiveSubscription()) {
                return redirect()->route('dashboard')->with('success', 'Welcome back! Your subscription is now active.');
            }
        }
        
        $daysRemaining = $user ? $user->getDaysRemaining() : 0;
        $status = $user ? $user->subscription_status : 'expired';
        
        return view('subscription.expired', compact('daysRemaining', 'status'));
    }
}
