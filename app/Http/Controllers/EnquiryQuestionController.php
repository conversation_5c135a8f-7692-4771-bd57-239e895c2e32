<?php

namespace App\Http\Controllers;

use App\Models\EnquiryQuestion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EnquiryQuestionController extends Controller
{
    public function index()
    {
        $questions = EnquiryQuestion::where('user_id', Auth::id())->orderBy('order')->get();
        return view('enquiries.configure', compact('questions'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string',
            'type' => 'required|in:text,date,radio,checkbox',
            'options' => 'nullable|array',
            'options.*' => 'nullable|string',
        ]);

        $nextOrder = EnquiryQuestion::where('user_id', Auth::id())->max('order') + 1;

        $question = new EnquiryQuestion([
            'user_id' => Auth::id(),
            'title' => $request->title,
            'type' => $request->type,
            'order' => $nextOrder,
        ]);

        if(in_array($request->type, ['radio', 'checkbox']) && $request->has('options')){
            $question->options = array_filter($request->options, 'strlen');
        }

        $question->save();

        return redirect()->route('enquiries.configure')->with('success', 'Question added successfully.');
    }

    public function destroy(EnquiryQuestion $enquiryQuestion)
    {
        if ($enquiryQuestion->user_id !== Auth::id()) {
            abort(403);
        }

        $enquiryQuestion->delete();
        return redirect()->route('enquiries.configure')->with('success', 'Question deleted successfully.');
    }
    public function updateOrder(Request $request)
    {
        $request->validate([
            'order' => 'required|array',
            'order.*' => 'integer',
        ]);
        foreach($request->order as $key => $value){
           $question = EnquiryQuestion::find($key);
            if($question && $question->user_id === Auth::id()){
                 $question->order = $value;
                 $question->save();
            }
        }
        return response()->json(['message' => 'Order Updated Successfully']);
    }
}