<?php

namespace App\Http\Controllers;

use App\Models\MarketingTeamMember;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\Enquiry;

class MarketingTeamMemberController extends Controller
{
    public function create()
    {
        return view('marketingteams.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:marketing_team_members',
            'password' => 'required|string|min:8',
        ]);

        MarketingTeamMember::create([
            'user_id' => Auth::id(),
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        return redirect()->route('marketingteams.index')->with('success', 'Team member added successfully.');
    }

    public function index()
    {
        $teamMembers = MarketingTeamMember::where('user_id', Auth::id())->get();
        return view('marketingteams.index', compact('teamMembers'));
    }
     public function destroy(MarketingTeamMember $marketingTeamMember)
    {
        if ($marketingTeamMember->user_id !== Auth::id()) {
            abort(403);
        }

        $marketingTeamMember->delete();

        return redirect()->route('marketingteams.index')->with('success', 'Marketing team member deleted successfully.');
    }


    //--- Marketing Panel Methods ---

    public function showLoginForm()
    {
        return view('marketing.login');
    }

    public function marketingLogin(Request $request)
    {
       $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if (Auth::guard('marketing')->attempt($credentials)) {
            $request->session()->regenerate();
            return redirect()->intended(route('marketing.dashboard'));
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ]);
    }

    public function dashboard()
    {
         if (!Auth::guard('marketing')->check()) {
            return redirect()->route('marketing.login.show');
        }
        // Get the currently authenticated marketing team member
        $marketingTeamMember = Auth::guard('marketing')->user();

        // Get enquiries assigned to the team member
        $enquiries = Enquiry::where('marketing_team_member_id', $marketingTeamMember->id)
        ->orderBy('created_at', 'desc')
        ->get();

        return view('marketing.dashboard', compact('enquiries'));
    }
      public function enquiries()
    {
       if (!Auth::guard('marketing')->check()) {
            return redirect()->route('marketing.login.show');
        }
        $marketingTeamMember = Auth::guard('marketing')->user(); // Get the logged-in team member.

        $enquiries = Enquiry::where('marketing_team_member_id', $marketingTeamMember->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('marketing.enquiries', compact('enquiries'));
    }
    public function show(Enquiry $enquiry)
    {
         if (!Auth::guard('marketing')->check()) {
            return redirect()->route('marketing.login.show');
        }
        if ($enquiry->marketing_team_member_id !== Auth::guard('marketing')->user()->id) {
            abort(403, 'Unauthorized access to enquiry.'); //Corrected
        }
        $enquiry->load('responses.question'); // Load questions with responses
        return response()->json($enquiry);
    }
     public function convertToCustomer(Enquiry $enquiry)
    {
         if (!Auth::guard('marketing')->check()) {
            return redirect()->route('marketing.login.show');
        }
        if ($enquiry->marketing_team_member_id !== Auth::guard('marketing')->user()->id) {
            abort(403);
        }

        $customer = Customer::create([
            'user_id' => $enquiry->user_id,
            'name' => $enquiry->name,
            'phone_number' => $enquiry->phone_number,
            // 'email' => $enquiry->email, // Assuming you might want to take this from somewhere else
        ]);

        ConvertedEnquiry::create([
            'user_id' => $enquiry->user_id,
            'enquiry_id' => $enquiry->id,
            'customer_id' => $customer->id,
        ]);

        $enquiry->delete();

        return redirect()->route('marketing.enquiries')->with('success', 'Enquiry converted to customer successfully.');
    }
     public function destroyEnquiry(Enquiry $enquiry)
    {
         if (!Auth::guard('marketing')->check()) {
            return redirect()->route('marketing.login.show');
        }
        if ($enquiry->marketing_team_member_id !== Auth::guard('marketing')->user()->id) {
           abort(403, 'Unauthorized to delete this enquiry');
        }
       $enquiry->delete();

       return redirect()->route('marketing.enquiries')->with('success', 'Enquiry deleted successfully.');
    }
    public function logout(Request $request)
    {
         Auth::guard('marketing')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }


}