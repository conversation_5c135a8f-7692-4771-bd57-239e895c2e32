<?php

namespace App\Http\Controllers;

use App\Models\LoyaltyConfiguration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoyaltyConfigurationController extends Controller
{
    public function index()
    {
        $loyaltyConfig = LoyaltyConfiguration::where('user_id', Auth::id())->first();

        if(!$loyaltyConfig) {
             $loyaltyConfig = new LoyaltyConfiguration(); // set default values when not exists
         }

        return view('loyalty_configurations.index', compact('loyaltyConfig'));
    }

    public function store(Request $request)
    {
         $request->validate([
             'point_name' => 'required|string',
             'earning_rate' => 'required|numeric|min:0',
              'spending_rate' => 'required|numeric|min:0',
         ]);

        $existingConfig = LoyaltyConfiguration::where('user_id', Auth::id())->first();
         if($existingConfig){
            $existingConfig->update([
                'point_name' => $request->point_name,
                'earning_rate' => $request->earning_rate,
                'spending_rate' => $request->spending_rate,
            ]);
          } else {
             LoyaltyConfiguration::create([
               'user_id' => Auth::id(),
                 'point_name' => $request->point_name,
                  'earning_rate' => $request->earning_rate,
                 'spending_rate' => $request->spending_rate,
            ]);
          }
          return redirect()->route('loyalty_configurations.index')->with('success', 'Loyalty configuration saved successfully.');
    }
}