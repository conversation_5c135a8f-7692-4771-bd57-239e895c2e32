<?php

namespace App\Http\Controllers;

use App\Models\Enquiry;
use App\Models\EnquiryQuestion;
use App\Models\EnquiryResponse;
use App\Models\Customer;
use App\Models\ConvertedEnquiry;
use App\Models\MarketingTeamMember; // Import the model
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EnquiryController extends Controller
{
   public function create()
    {
        $questions = EnquiryQuestion::where('user_id', Auth::id())->orderBy('order')->get();
        return view('enquiries.create', compact('questions'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string',
            'phone_number' => 'required|string',
            'lead_type' => 'required|in:hot,mild,cold',
            'additional_notes' => 'nullable|string',
            'responses' => 'nullable|array',
        ]);

        $enquiry = Enquiry::create([
            'user_id' => Auth::id(),
            'name' => $request->name,
            'phone_number' => $request->phone_number,
            'lead_type' => $request->lead_type,
            'additional_notes' => $request->additional_notes,
        ]);

        if ($request->has('responses')) {
            foreach ($request->responses as $questionId => $responseValue) {
                EnquiryResponse::create([
                    'enquiry_id' => $enquiry->id,
                    'enquiry_question_id' => $questionId,
                    'response' => is_string($responseValue) ? $responseValue : json_encode($responseValue),
                ]);
            }
        }

        return redirect()->route('enquiries.manage')->with('success', 'Enquiry added successfully.');
    }

    public function manage(Request $request)
    {
        $userId = Auth::id();
        $convertedEnquiryIds = ConvertedEnquiry::where('user_id', $userId)->pluck('enquiry_id')->toArray();
        $enquiries = Enquiry::where('user_id', $userId)
            ->whereNotIn('id', $convertedEnquiryIds)
            ->when($request->has('lead_type') && in_array($request->input('lead_type'), ['hot', 'mild', 'cold']), function ($query) use ($request) {
                $query->where('lead_type', $request->input('lead_type'));
            })
            ->when($request->has('sort_by'), function ($query) use ($request) {
                if ($request->input('sort_by') === 'newest') {
                    $query->orderBy('created_at', 'desc');
                } elseif ($request->input('sort_by') === 'oldest') {
                    $query->orderBy('created_at', 'asc');
                }
            })
             ->when($request->has('marketing_team_member_id'), function ($query) use ($request) {
                $query->where('marketing_team_member_id', $request->input('marketing_team_member_id'));
            })
            ->paginate(10);

        $teamMembers = MarketingTeamMember::where('user_id', Auth::id())->get(); // Get team members for the dropdown

        return view('enquiries.manage', compact('enquiries', 'teamMembers'));
    }

    public function show(Enquiry $enquiry)
    {
        if ($enquiry->user_id !== Auth::id()) {
            abort(403);
        }
        $enquiry->load('responses.question'); // Load questions with responses
        return response()->json($enquiry);
    }

    public function convertToCustomer(Enquiry $enquiry)
    {
        if ($enquiry->user_id !== Auth::id()) {
            abort(403);
        }

        $customer = Customer::create([
            'user_id' => Auth::id(),
            'name' => $enquiry->name,
            'phone_number' => $enquiry->phone_number,
            // 'email' => $enquiry->email, // Assuming you might want to take this from somewhere else
        ]);

        ConvertedEnquiry::create([
            'user_id' => Auth::id(),
            'enquiry_id' => $enquiry->id,
            'customer_id' => $customer->id,
        ]);

        $enquiry->delete();

        return redirect()->route('customers.index')->with('success', 'Enquiry converted to customer successfully.');
    }

   public function destroy(Enquiry $enquiry)
    {
        if ($enquiry->user_id !== Auth::id()) {
            abort(403);
        }
        $enquiry->delete();

       return redirect()->route('enquiries.manage')->with('success', 'Enquiry deleted successfully.');
    }

     public function assign(Request $request, Enquiry $enquiry)
    {

        if ($enquiry->user_id !== Auth::id()) {
            abort(403); // Or handle as you see fit
        }

        $request->validate([
            'marketing_team_member_id' => 'nullable|exists:marketing_team_members,id',
        ]);


        $enquiry->marketing_team_member_id = $request->marketing_team_member_id;
        $enquiry->save();

        return redirect()->back()->with('success', 'Enquiry assigned successfully.');
    }
}