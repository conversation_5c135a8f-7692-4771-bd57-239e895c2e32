<?php

namespace App\Http\Controllers;

use App\Models\Template;
use App\Services\SupabaseStorageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Spatie\Browsershot\Browsershot;
use Symfony\Component\Process\ExecutableFinder;
use Illuminate\Support\Str;

class TemplateController extends Controller
{
    public function index()
    {
        $templates = Template::where('user_id', Auth::id())->get();
        return view('templates.index', compact('templates'));
    }

    public function create()
    {
        return view('templates.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'layout' => 'required|in:layout1,layout2',
            'background_color' => 'nullable|string',
            'image' => 'nullable|image|max:2048',
        ]);

        $supabaseService = new SupabaseStorageService();

        // Get the HTML content
        $content = $request->input('content');
        
        // Process uploaded image if exists
        $uploadedImagePath = null;
        $uploadedImageSupabaseUrl = null;
        if ($request->hasFile('image')) {
            // Upload to local storage (fallback)
            $uploadedImagePath = $request->file('image')->store('template_images', 'public');
            
            // Upload to Supabase
            $supabaseResult = $supabaseService->uploadFile($request->file('image'), 'template_images');
            if ($supabaseResult['success']) {
                $uploadedImageSupabaseUrl = $supabaseResult['url'];
                // Update image URL in content
                $content = preg_replace(
                    '/src="[^"]*"/',
                    'src="' . $uploadedImageSupabaseUrl . '"',
                    $content,
                    1
                );
            } else {
                // Fallback to local storage
                $imageUrl = Storage::url($uploadedImagePath);
                $content = preg_replace(
                    '/src="[^"]*"/',
                    'src="' . $imageUrl . '"',
                    $content,
                    1
                );
            }
        }

        // Enhance HTML with proper styling
        $styledContent = $this->wrapWithStyles($content);

        $finder = new ExecutableFinder();
        $nodePath = $finder->find('node');
        $npmPath = $finder->find('npm');

        if (!$nodePath) {
            return back()->withErrors(['node' => 'Node.js not found. Please ensure it is installed and in your system\'s PATH.']);
        }
        if (!$npmPath) {
            return back()->withErrors(['npm' => 'npm not found. Please ensure it is installed and in your system\'s PATH.']);
        }

        // Generate the template preview image
        $imageName = 'template_renders/template_' . Auth::id() . '_' . time() . '.png';
        $localImagePath = 'template_renders/' . basename($imageName);

        try {
            // Generate image locally first
            $localPath = storage_path('app/public/' . $localImagePath);
            Browsershot::html($styledContent)
                ->setNodeBinary($nodePath)
                ->setNpmBinary($npmPath)
                ->windowSize(640, 860)
                ->clip(0, 0, 640, 860)
                ->setOption('omitBackground', true)
                ->save($localPath);

            // Upload generated image to Supabase
            $imageContent = file_get_contents($localPath);
            $supabaseImageResult = $supabaseService->uploadFileContent($imageContent, $imageName, 'image/png');
            
            $contentImageSupabaseUrl = null;
            if ($supabaseImageResult['success']) {
                $contentImageSupabaseUrl = $supabaseImageResult['url'];
            }

        } catch (\Exception $e) {
            return back()->withErrors(['browsershot' => 'Error generating template image: ' . $e->getMessage()]);
        }

        Template::create([
            'user_id' => Auth::id(),
            'name' => $request->name,
            'layout' => $request->layout,
            'content' => $styledContent,  // Store the full styled HTML
            'content_image' => $localImagePath, // Store the local rendered preview image path (fallback)
            'content_image_supabase_url' => $contentImageSupabaseUrl, // Store Supabase URL
            'background_color' => $request->background_color ?? '#0891b2',
        ]);

        return redirect()->route('templates.index')->with('success', 'Template created successfully.');
    }

    public function show(Template $template)
    {
        if ($template->user_id != Auth::id()) {
            abort(403);
        }
        
        return view('templates.show', compact('template'));
    }

    public function edit(Template $template)
    {
        if ($template->user_id != Auth::id()) {
            abort(403);
        }
        
        return view('templates.edit', compact('template'));
    }

    public function update(Request $request, Template $template)
    {
        if ($template->user_id != Auth::id()) {
            abort(403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'layout' => 'required|in:layout1,layout2',
            'background_color' => 'nullable|string',
            'image' => 'nullable|image|max:2048',
        ]);

        $supabaseService = new SupabaseStorageService();
        $content = $request->input('content');

        if ($content) {
            // Process uploaded image if exists
            if ($request->hasFile('image')) {
                // Delete old local image if exists
                if ($template->content_image) {
                    Storage::disk('public')->delete($template->content_image);
                }
                
                // Upload to local storage (fallback)
                $uploadedImagePath = $request->file('image')->store('template_images', 'public');
                
                // Upload to Supabase
                $supabaseResult = $supabaseService->uploadFile($request->file('image'), 'template_images');
                if ($supabaseResult['success']) {
                    $uploadedImageSupabaseUrl = $supabaseResult['url'];
                    // Update image URL in content
                    $content = preg_replace(
                        '/src="[^"]*"/',
                        'src="' . $uploadedImageSupabaseUrl . '"',
                        $content,
                        1
                    );
                } else {
                    // Fallback to local storage
                    $imageUrl = Storage::url($uploadedImagePath);
                    $content = preg_replace(
                        '/src="[^"]*"/',
                        'src="' . $imageUrl . '"',
                        $content,
                        1
                    );
                }
            }

            // Enhance HTML with proper styling
            $styledContent = $this->wrapWithStyles($content);

            $finder = new ExecutableFinder();
            $nodePath = $finder->find('node');
            $npmPath = $finder->find('npm');

            if (!$nodePath) {
                return back()->withErrors(['node' => 'Node.js not found. Please ensure it is installed and in your system\'s PATH.']);
            }
            if (!$npmPath) {
                return back()->withErrors(['npm' => 'npm not found. Please ensure it is installed and in your system\'s PATH.']);
            }

            // Upload the template HTML content to Supabase
            $templateName = 'templates/template_' . Auth::id() . '_' . time() . '.html';
            $supabaseResult = $supabaseService->uploadFileContent($styledContent, $templateName, 'text/html');
            
            if (!$supabaseResult['success']) {
                return back()->withErrors(['supabase' => 'Error uploading template: ' . $supabaseResult['error']]);
            }

            $template->content = $styledContent;
            $template->content_image_supabase_url = $supabaseResult['url'];
        }

        $template->name = $request->name;
        $template->layout = $request->layout;
        $template->background_color = $request->background_color ?? '#0891b2';
        $template->save();

        return redirect()->route('templates.index')->with('success', 'Template updated successfully.');
    }

    public function destroy(Template $template)
    {
        if ($template->user_id != Auth::id()) {
            abort(403);
        }

        // Delete the preview image
        if ($template->content_image) {
            Storage::disk('public')->delete($template->content_image);
        }
        
        $template->delete();
        return redirect()->route('templates.index')->with('success', 'Template deleted successfully.');
    }
    
    /**
     * Wrap HTML content with necessary styles to ensure proper rendering
     */
    private function wrapWithStyles($html)
    {
        return '<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Template</title>
            <script src="https://cdn.tailwindcss.com"></script>
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body { margin: 0; padding: 0; width: 100%; height: 100%; }
                .fill-current { fill: currentColor; }
                button {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 0.375rem;
                    padding: 0.5rem 1rem;
                    font-weight: 500;
                    transition-property: color, background-color, border-color;
                    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
                    transition-duration: 150ms;
                }
                img {
                    display: block;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
                .max-w-md {
                    max-width: none !important;
                    width: 100% !important;
                }
                .rounded-xl {
                    border-radius: 0 !important;
                }
            </style>
        </head>
        <body class="w-full h-full">' . $html . '</body></html>';
    }
}