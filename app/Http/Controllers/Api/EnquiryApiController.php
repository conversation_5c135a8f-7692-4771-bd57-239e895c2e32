<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Enquiry;
use App\Models\EnquiryResponse;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class EnquiryApiController extends Controller
{
    public function store(Request $request)
    {
        // Validate API key
        $apiKey = $request->header('X-API-KEY');
        if (!$apiKey) {
            return response()->json(['error' => 'API key is required'], 401);
        }

        $user = User::where('api_key', $apiKey)->first();
        if (!$user) {
            return response()->json(['error' => 'Invalid API key'], 401);
        }

        // Validate request data
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'phone_number' => 'required|string|max:20',
            'lead_type' => 'required|in:hot,mild,cold',
            'additional_notes' => 'nullable|string',
            'responses' => 'nullable|array',
            'responses.*.question_id' => 'required|exists:enquiry_questions,id',
            'responses.*.response' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        // Create enquiry
        $enquiry = Enquiry::create([
            'user_id' => $user->id,
            'name' => $request->name,
            'phone_number' => $request->phone_number,
            'lead_type' => $request->lead_type,
            'additional_notes' => $request->additional_notes,
        ]);

        // Store responses if any
        if ($request->has('responses')) {
            foreach ($request->responses as $response) {
                EnquiryResponse::create([
                    'enquiry_id' => $enquiry->id,
                    'enquiry_question_id' => $response['question_id'],
                    'response' => $response['response'],
                ]);
            }
        }

        return response()->json([
            'message' => 'Enquiry created successfully',
            'enquiry' => $enquiry->load('responses'),
        ], 201);
    }
}
