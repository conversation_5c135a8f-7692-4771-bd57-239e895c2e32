<?php

namespace App\Http\Controllers;

use App\Models\CustomField;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CustomFieldController extends Controller
{
    public function index()
    {
        $customFields = CustomField::where('user_id', Auth::id())->latest()->paginate(10);
        return view('custom_fields.index', compact('customFields'));
    }

    public function create()
    {
        return view('custom_fields.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string',
            'type' => 'required|in:text,date,radio,checkbox',
            'options' => 'nullable|array',
           'options.*' => 'nullable|string',
       ]);

        $customField = new CustomField([
            'user_id' => Auth::id(),
            'title' => $request->title,
            'type' => $request->type,
        ]);

        if(in_array($request->type, ['radio', 'checkbox']) && $request->has('options')){
            $customField->options = array_filter($request->options, 'strlen');
       }

       $customField->save();

        return redirect()->route('custom_fields.index')->with('success', 'Custom Field created successfully.');
    }

    public function edit(CustomField $customField)
    {
       if ($customField->user_id !== Auth::id()) {
            abort(403); // Unauthorized access
        }
        return view('custom_fields.edit', compact('customField'));
    }

    public function update(Request $request, CustomField $customField)
    {
         if ($customField->user_id !== Auth::id()) {
            abort(403); // Unauthorized access
        }

        $request->validate([
           'title' => 'required|string',
           'type' => 'required|in:text,date,radio,checkbox',
            'options' => 'nullable|array',
            'options.*' => 'nullable|string',
        ]);


        $customField->title = $request->title;
        $customField->type = $request->type;

        if(in_array($request->type, ['radio', 'checkbox']) && $request->has('options')){
            $customField->options = array_filter($request->options, 'strlen');
         } else {
            $customField->options = null;
       }
        $customField->save();

        return redirect()->route('custom_fields.index')->with('success', 'Custom Field updated successfully.');
    }

    public function destroy(CustomField $customField)
    {
        if ($customField->user_id !== Auth::id()) {
             abort(403); // Unauthorized access
        }
        $customField->delete();
        return redirect()->route('custom_fields.index')->with('success', 'Custom Field deleted successfully.');
    }
}