<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\CustomerCustomFieldValue;
use App\Models\CustomField;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\StreamedResponse;

class CustomerController extends Controller
{
    public function index()
    {
       $customers = Customer::where('user_id', Auth::id())->with('loyaltyPoints')->latest()->paginate(10);
        return view('customers.index', compact('customers'));
    }

    public function create()
    {
       $tags = Tag::where('user_id', Auth::id())->get();
        $customFields = CustomField::where('user_id', Auth::id())->get();
        return view('customers.create', compact('tags', 'customFields'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string',
            'phone_number' => 'required|string',
            'email' => 'nullable|email',
            'tags' => 'nullable|array',
            'date_of_birth' => 'nullable|date',
           'anniversary_date' => 'nullable|date',
        ]);

       $existingCustomer = Customer::where('user_id', Auth::id())
            ->where('phone_number', $request->phone_number)
            ->first();

        if ($existingCustomer) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['phone_number' => 'A customer with this phone number already exists.']);
        }

        $customer = Customer::create([
            'user_id' => Auth::id(),
            'name' => $request->name,
            'phone_number' => $request->phone_number,
            'email' => $request->email,
           'date_of_birth' => $request->date_of_birth,
            'anniversary_date' => $request->anniversary_date,
        ]);

        if ($request->has('tags')) {
            $customer->tags()->attach($request->tags);
        }

        if($request->has('custom_fields')){
            foreach($request->custom_fields as $fieldId => $fieldValue)
            {
                CustomerCustomFieldValue::create([
                    'customer_id' => $customer->id,
                    'custom_field_id' => $fieldId,
                    'value' => $fieldValue
                ]);
            }
        }

        return redirect()->route('customers.index')->with('success', 'Customer added successfully.');
    }

   public function edit(Customer $customer)
    {
        if ($customer->user_id !== Auth::id()) {
            abort(403); // Unauthorized access
        }

       $tags = Tag::where('user_id', Auth::id())->get();
        $customFields = CustomField::where('user_id', Auth::id())->get();
        $customer->load('tags', 'customFieldValues');
        return view('customers.edit', compact('customer','tags','customFields'));
    }

   public function update(Request $request, Customer $customer)
    {
        if ($customer->user_id !== Auth::id()) {
            abort(403); // Unauthorized access
        }
        $request->validate([
           'name' => 'required|string',
            'phone_number' => 'required|string',
            'email' => 'nullable|email',
            'tags' => 'nullable|array',
            'date_of_birth' => 'nullable|date',
            'anniversary_date' => 'nullable|date',
        ]);

        $existingCustomer = Customer::where('user_id', Auth::id())
            ->where('phone_number', $request->phone_number)
            ->where('id', '!=', $customer->id)
            ->first();

       if ($existingCustomer) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['phone_number' => 'A customer with this phone number already exists.']);
        }

        $customer->update([
            'name' => $request->name,
           'phone_number' => $request->phone_number,
            'email' => $request->email,
            'date_of_birth' => $request->date_of_birth,
            'anniversary_date' => $request->anniversary_date,
       ]);
        if ($request->has('tags')) {
            $customer->tags()->sync($request->tags);
        } else {
            $customer->tags()->detach();
        }
        if($request->has('custom_fields'))
        {
            foreach($request->custom_fields as $fieldId => $fieldValue)
            {
                $customerCustomFieldValue = CustomerCustomFieldValue::where('customer_id', $customer->id)
                    ->where('custom_field_id', $fieldId)
                   ->first();
                if($customerCustomFieldValue){
                    $customerCustomFieldValue->update([
                       'value' => $fieldValue
                    ]);
                }else{
                   CustomerCustomFieldValue::create([
                        'customer_id' => $customer->id,
                        'custom_field_id' => $fieldId,
                       'value' => $fieldValue
                    ]);
                }
            }
        }
        return redirect()->route('customers.index')->with('success', 'Customer updated successfully.');
   }

    public function destroy(Customer $customer)
    {
        if ($customer->user_id !== Auth::id()) {
            abort(403); // Unauthorized access
        }
        $customer->delete();
        return redirect()->route('customers.index')->with('success', 'Customer deleted successfully.');
    }
    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv',
        ]);
        $file = $request->file('file');
        $filePath = $file->store('import-customers');
        $fullFilePath = Storage::path($filePath);
        $rows = array_map('str_getcsv', file($fullFilePath));
        $header = array_shift($rows);

        foreach ($rows as $row) {
            if (count($row) >= 2 && isset($header[0]) && isset($header[1])) {
                $customerData = array_combine($header, $row);

                if (empty($customerData['name']) || empty($customerData['phone_number'])) {
                    continue;
                }
               $existingCustomer = Customer::where('user_id', Auth::id())
                   ->where('phone_number', $customerData['phone_number'])
                   ->first();

               if (!$existingCustomer) {
                    $customer = Customer::create([
                        'user_id' => Auth::id(),
                        'name' => $customerData['name'],
                        'phone_number' => $customerData['phone_number'],
                       'email' => $customerData['email'] ?? null,
                        'date_of_birth' => $customerData['date_of_birth'] ?? null,
                        'anniversary_date' => $customerData['anniversary_date'] ?? null,
                   ]);

                    if(!empty($customerData['tags'])){
                        $tags = array_map('trim', explode(',', $customerData['tags']));
                       $tagIds = [];
                        foreach($tags as $tag){
                            $existingTag = Tag::where('user_id', Auth::id())->where(DB::raw('lower(name)'), strtolower($tag))->first();
                            if($existingTag){
                                $tagIds[] = $existingTag->id;
                           }
                        }
                       $customer->tags()->attach($tagIds);
                    }
                }
            }
        }
        Storage::delete($filePath);
        return redirect()->route('customers.index')->with('success', 'Customers imported successfully.');
    }
    public function export()
    {
        $userId = Auth::id();
        $filename = 'customers_' . now()->format('Ymd_His') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
           'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];
        $callback = function () use ($userId) {
            $output = fopen('php://output', 'w');
            fputcsv($output, ['Name', 'Phone Number', 'Email', 'Tags', 'Date of Birth', 'Anniversary Date']);
            $customers = Customer::where('user_id', $userId)->with('tags')->get();
            foreach ($customers as $customer) {
                $tags = $customer->tags->pluck('name')->implode(', ');
                fputcsv($output, [
                    $customer->name,
                   $customer->phone_number,
                    $customer->email,
                    $tags,
                    $customer->date_of_birth,
                    $customer->anniversary_date,
                ]);
            }
            fclose($output);
        };

        return new StreamedResponse($callback, 200, $headers);
    }
    public function sample()
    {
       $filename = 'sample_customers.csv';
        $headers = [
            'Content-Type' => 'text/csv',
           'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];
        $callback = function () {
            $output = fopen('php://output', 'w');
            fputcsv($output, ['name', 'phone_number', 'email', 'tags', 'date_of_birth', 'anniversary_date']);
           fputcsv($output, ['John Doe', '9876543210', '<EMAIL>', 'tag1, tag2', '1990-01-15', '2022-05-20']);
            fputcsv($output, ['Jane Smith', '8765432109', '<EMAIL>', 'tag3, tag4', '1995-06-10', '2023-12-25']);
            fclose($output);
        };

        return new StreamedResponse($callback, 200, $headers);
    }
}