<?php

namespace App\Http\Controllers;

use App\Models\CustomField;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FieldsAndTagsController extends Controller
{
    public function index()
    {
        $customFields = CustomField::where('user_id', Auth::id())->get();
        $tags = Tag::where('user_id', Auth::id())->get();
        return view('fields_and_tags.index', compact('customFields', 'tags'));
    }

    public function storeField(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'type' => 'required|in:text,number,date,radio,checkbox',
            'options' => 'required_if:type,radio,checkbox|nullable|string',
        ]);

        CustomField::create([
            'user_id' => Auth::id(),
            'title' => $request->title,
            'type' => $request->type,
            'options' => in_array($request->type, ['radio', 'checkbox']) ? $request->options : null,
        ]);

        return redirect()->route('fields-and-tags.index')->with('success', 'Custom field added successfully.');
    }

    public function storeTag(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        Tag::create([
            'user_id' => Auth::id(),
            'name' => $request->name,
        ]);

        return redirect()->route('fields-and-tags.index')->with('success', 'Tag added successfully.');
    }

    public function updateField(Request $request, CustomField $field)
    {
        if ($field->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'type' => 'required|in:text,number,date,radio,checkbox',
            'options' => 'required_if:type,radio,checkbox|nullable|string',
        ]);

        $field->update([
            'title' => $request->title,
            'type' => $request->type,
            'options' => in_array($request->type, ['radio', 'checkbox']) ? $request->options : null,
        ]);

        return redirect()->route('fields-and-tags.index')->with('success', 'Custom field updated successfully.');
    }

    public function updateTag(Request $request, Tag $tag)
    {
        if ($tag->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $tag->update([
            'name' => $request->name,
        ]);

        return redirect()->route('fields-and-tags.index')->with('success', 'Tag updated successfully.');
    }

    public function destroyField(CustomField $field)
    {
        if ($field->user_id !== Auth::id()) {
            abort(403);
        }

        $field->delete();
        return redirect()->route('fields-and-tags.index')->with('success', 'Custom field deleted successfully.');
    }

    public function destroyTag(Tag $tag)
    {
        if ($tag->user_id !== Auth::id()) {
            abort(403);
        }

        $tag->delete();
        return redirect()->route('fields-and-tags.index')->with('success', 'Tag deleted successfully.');
    }
}
