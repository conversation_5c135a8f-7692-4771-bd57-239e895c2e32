<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Review;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;

class ReviewController extends Controller
{
    public function index()
    {
       $googleReviewLink = Auth::user()->google_review_link;
        $companyName = Auth::user()->company_name;
      $reviews = Review::where('user_id', Auth::id())
          ->where('rating', '<=', 3)
           ->latest()
          ->paginate(10);

        return view('reviews.index', compact('googleReviewLink', 'companyName', 'reviews'));
    }

   public function store(Request $request)
    {
        $request->validate([
             'rating' => 'required|integer|min:1|max:5',
              'review' => 'nullable|string',
              'name' => 'nullable|string',
             'phone' => 'nullable|string',
         ]);
        $user = User::find($request->user_id);

          if ($request->rating > 3) {
             return redirect($user->google_review_link);
          }

         $customer = Customer::where('user_id', $request->user_id)->where('phone_number', $request->phone)->first();

          if(!$customer) {
             $customer = Customer::create([
                'user_id' => $request->user_id,
                 'name' => $request->name,
                  'phone_number' => $request->phone,
             ]);
         }


          Review::create([
               'user_id' => $request->user_id,
              'name' => $request->name,
              'phone_number' => $request->phone,
               'review' => $request->review,
               'rating' => $request->rating,
         ]);

          $this->sendWhatsappMessage($user->phone_number, $user->company_name, $request->name, $request->phone, $request->review, $request->rating);

          return response()->json(['message' => 'Review submitted successfully']);
     }
      private function sendWhatsappMessage($phoneNumber, $companyName, $name, $phone, $review, $rating)
      {
         $message = "New Feedback received for {$companyName} \n \n Name : {$name} \n Phone : {$phone} \n Rating : {$rating} \n Review : {$review}";
              $response = Http::post('https://wa.connectezee.com/api/send', [
                 "number" => $phoneNumber,
                  "type" => "text",
                  "message" => $message,
                 "instance_id" => "66E68C43737FD",
                "access_token" => "66b1ad7d11db0"
             ]);

        }
     public function reviewPage($userId)
     {
          $user = User::find($userId);
         if(!$user){
              abort(404);
          }
         return view('reviews.review-page', compact('user'));
     }
 }