<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Purchase;
use App\Models\Tag;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
       $userId = Auth::id();
       // Sales Data for Chart
       $salesData = Purchase::where('user_id', $userId)
           ->select(DB::raw('DATE(created_at) as date'), DB::raw('SUM(amount) as total'))
           ->groupBy('date')
           ->orderBy('date')
           ->get()
           ->toArray();
        $salesLabels = array_column($salesData, 'date');
        $salesValues = array_column($salesData, 'total');

       // Active/Inactive Customers
       $activeCustomersCount = Customer::where('user_id', $userId)->whereHas('purchases')->count();
       $inactiveCustomersCount = Customer::where('user_id', $userId)->whereDoesntHave('purchases')->count();


      // Customers per Tag Data
      $tagData = Tag::where('user_id', $userId)
          ->withCount('customers')
           ->get()
           ->toArray();
        $tagLabels = array_column($tagData, 'name');
        $tagValues = array_column($tagData, 'customers_count');


     $totalCustomers = Customer::where('user_id', $userId)->count();

       $totalSales = Purchase::where('user_id', $userId)->sum('amount');
       
       // Get or generate API key
       $user = Auth::user();
       if (!$user->api_key) {
           $user->api_key = bin2hex(random_bytes(32));
           $user->save();
       }
       
        return view('dashboard', compact('salesLabels', 'salesValues', 'activeCustomersCount', 'inactiveCustomersCount', 'tagLabels', 'tagValues', 'totalCustomers', 'totalSales', 'user'));
   }
}