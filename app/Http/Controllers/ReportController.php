<?php

namespace App\Http\Controllers;

use App\Models\Purchase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReportController extends Controller
{
    public function index(Request $request)
    {

    $query = Purchase::where('user_id', Auth::id())->with('customer');
        if ($request->has('sort')) {
            $sortField = $request->input('sort');
            $sortDirection = $request->input('direction', 'asc');
            if(in_array($sortField, ['created_at', 'amount'])) {
                $query->orderBy($sortField, $sortDirection);
            }
        }
        $purchases = $query->latest()->paginate(10);


        return view('reports.index', compact('purchases'));
    }
}