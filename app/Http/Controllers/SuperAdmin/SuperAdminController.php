<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SuperAdminController extends Controller
{
    public function showLoginForm()
    {
        return view('super_admin.login');
    }

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        if (Auth::guard('super_admin')->attempt($credentials)) {
            $request->session()->regenerate();
            return redirect()->intended(route('super_admin.dashboard'));
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->onlyInput('email');
    }

    public function dashboard()
    {
        $users = User::with(['customers', 'purchases'])
            ->withCount(['customers', 'purchases'])
            ->latest()
            ->paginate(10);

        return view('super_admin.dashboard', compact('users'));
    }

    public function updateSubscription(Request $request, User $user)
    {
        $request->validate([
            'subscription_status' => 'required|in:trial,active,expired,disabled',
            'subscription_ends_at' => 'nullable|date',
            'trial_ends_at' => 'nullable|date',
            'is_active' => 'required|boolean',
        ]);

        $user->update([
            'subscription_status' => $request->subscription_status,
            'subscription_ends_at' => $request->subscription_ends_at,
            'trial_ends_at' => $request->trial_ends_at,
            'is_active' => $request->is_active,
        ]);

        return redirect()->back()->with('success', 'User subscription updated successfully.');
    }

    public function extendSubscription(Request $request, User $user)
    {
        $request->validate([
            'days' => 'required|integer|min:1|max:365',
        ]);

        $days = (int) $request->days;

        if ($user->subscription_status === 'trial') {
            $user->trial_ends_at = $user->trial_ends_at ? 
                $user->trial_ends_at->addDays($days) : 
                now()->addDays($days);
        } else {
            $user->subscription_ends_at = $user->subscription_ends_at ? 
                $user->subscription_ends_at->addDays($days) : 
                now()->addDays($days);
        }

        $user->save();

        return redirect()->back()->with('success', "User subscription extended by {$days} days.");
    }

    public function toggleUserStatus(User $user)
    {
        $user->update(['is_active' => !$user->is_active]);
        
        $status = $user->is_active ? 'enabled' : 'disabled';
        return redirect()->back()->with('success', "User account {$status} successfully.");
    }

    public function logout(Request $request)
    {
        Auth::guard('super_admin')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect()->route('super_admin.login');
    }
}
