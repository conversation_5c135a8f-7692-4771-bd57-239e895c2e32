<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ApiDocumentationController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        if (!$user->api_key) {
            $user->api_key = bin2hex(random_bytes(32));
            $user->save();
        }
        
        return view('api.documentation', compact('user'));
    }
}
