<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckSubscription
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();
        
        if ($user) {
            // Update subscription status
            $user->updateSubscriptionStatus();
            
            // Check if subscription is active
            if (!$user->hasActiveSubscription()) {
                return redirect()->route('subscription.expired');
            }
        }
        
        return $next($request);
    }
}
