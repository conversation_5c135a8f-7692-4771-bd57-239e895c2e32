<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'supabase' => [
        'url' => env('SUPABASE_URL', 'https://fhgowvbaaviyizgwwuyd.supabase.co'),
        'anon_key' => env('SUPABASE_ANON_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZoZ293dmJhYXZpeWl6Z3d3dXlkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5NTE1MzMsImV4cCI6MjA2NzUyNzUzM30.Y5FCIT7EHQUoA7mgVvTlqfWtxG7MpTbmDiTXChg2K-w'),
        'service_role_key' => env('SUPABASE_SERVICE_ROLE_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZoZ293dmJhYXZpeWl6Z3d3dXlkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTk1MTUzMywiZXhwIjoyMDY3NTI3NTMzfQ.QDSfWidkZYG76jPaTqSCklq9WI0nnepFWbZPljnodjI'),
        'bucket_name' => env('SUPABASE_BUCKET_NAME', 'cms-storage'),
    ],

];
