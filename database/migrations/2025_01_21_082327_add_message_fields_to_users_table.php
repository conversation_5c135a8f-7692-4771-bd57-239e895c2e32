<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
           $table->string('purchase_message')->nullable()->after('google_review_link');
           $table->string('birthday_message')->nullable()->after('purchase_message');
            $table->string('anniversary_message')->nullable()->after('birthday_message');
        });
   }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('purchase_message');
            $table->dropColumn('birthday_message');
            $table->dropColumn('anniversary_message');
            $table->dropColumn('phone_number');
       });
    }
};