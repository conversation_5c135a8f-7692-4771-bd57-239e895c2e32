<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('enquiries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('phone_number');
            $table->enum('lead_type', ['hot', 'mild', 'cold']);
            $table->text('additional_notes')->nullable();
            $table->foreignId('marketing_team_member_id')->nullable()->constrained()->onDelete('set null'); // Add this line
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('enquiries', function(Blueprint $table){
            $table->dropForeign(['marketing_team_member_id']);
            $table->dropColumn('marketing_team_member_id');
        });
        Schema::dropIfExists('enquiries');
    }
};