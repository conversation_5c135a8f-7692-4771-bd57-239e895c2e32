<?php

   use Illuminate\Database\Migrations\Migration;
   use Illuminate\Database\Schema\Blueprint;
   use Illuminate\Support\Facades\Schema;

   return new class extends Migration
   {
       /**
        * Run the migrations.
        */
       public function up(): void
       {
           Schema::create('purchases', function (Blueprint $table) {
               $table->id();
               $table->foreignId('user_id')->constrained()->onDelete('cascade');
               $table->foreignId('customer_id')->constrained()->onDelete('cascade');
               $table->decimal('amount', 10, 2);
               $table->decimal('discount', 10, 2)->default(0);
               $table->integer('loyalty_points_spent')->default(0);
               $table->timestamps();
           });
       }

       /**
        * Reverse the migrations.
        */
       public function down(): void
       {
           Schema::dropIfExists('purchases');
       }
   };