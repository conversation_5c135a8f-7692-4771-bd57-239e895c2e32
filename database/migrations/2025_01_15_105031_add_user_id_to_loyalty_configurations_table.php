<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('loyalty_configurations', function (Blueprint $table) {
            $table->foreignId('user_id')->constrained()->onDelete('cascade')->after('id');
             $table->string('point_name')->default('Loyalty Point')->nullable()->after('user_id');
            $table->decimal('earning_rate', 5, 2)->default(0.5)->nullable()->after('point_name');
            $table->decimal('spending_rate', 5, 2)->default(1)->nullable()->after('earning_rate');
            $table->unique('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('loyalty_configurations', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->dropUnique(['user_id']);
            $table->dropColumn('user_id');
            $table->dropColumn('point_name');
            $table->dropColumn('earning_rate');
            $table->dropColumn('spending_rate');

        });
    }
};