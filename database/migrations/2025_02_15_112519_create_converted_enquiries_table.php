<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
     /**
      * Run the migrations.
      */
    public function up(): void
      {
          Schema::create('converted_enquiries', function (Blueprint $table) {
              $table->id();
             $table->foreignId('user_id')->constrained()->onDelete('cascade');
              $table->foreignId('enquiry_id')->constrained()->onDelete('cascade');
              $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null'); // If you want to track the converted customer
              $table->timestamps();
          });
      }

      /**
       * Reverse the migrations.
       */
    public function down(): void
      {
          Schema::dropIfExists('converted_enquiries');
      }
};