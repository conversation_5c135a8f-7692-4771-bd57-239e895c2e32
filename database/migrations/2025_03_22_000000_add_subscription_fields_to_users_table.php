<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->timestamp('trial_ends_at')->nullable()->after('email_verified_at');
            $table->timestamp('subscription_ends_at')->nullable()->after('trial_ends_at');
            $table->boolean('is_active')->default(true)->after('subscription_ends_at');
            $table->enum('subscription_status', ['trial', 'active', 'expired', 'disabled'])->default('trial')->after('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['trial_ends_at', 'subscription_ends_at', 'is_active', 'subscription_status']);
        });
    }
};
