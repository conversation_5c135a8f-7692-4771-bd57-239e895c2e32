<?php

use App\Http\Controllers\CampaignController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\CustomFieldController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\LoyaltyConfigurationController;
use App\Http\Controllers\PurchaseController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\CustomerInsightController;
use App\Http\Controllers\ReviewController;
use App\Http\Controllers\EnquiryController;
use App\Http\Controllers\EnquiryQuestionController;
use App\Http\Controllers\FrontDeskController;
use App\Http\Controllers\MarketingTeamMemberController; // Import
use App\Http\Controllers\ApiDocumentationController; // Import API doc controller
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

//Frontdesk routes - Public
Route::get('/frontdesk', [FrontDeskController::class, 'showLoginForm'])->name('frontdesk.login.show');
Route::post('/frontdesk', [FrontDeskController::class, 'frontdeskLogin'])->name('frontdesk.login');
Route::get('/frontdesk/dashboard', [FrontDeskController::class, 'dashboard'])->name('frontdesk.dashboard');
Route::get('/frontdesk/accounting', [FrontDeskController::class, 'accounting'])->name('frontdesk.accounting');
Route::get('/frontdesk/enquiry', [FrontDeskController::class, 'enquiry'])->name('frontdesk.enquiry');

//Reviews Route
Route::get('/reviews/form/{userId}', [ReviewController::class, 'reviewPage'])->name('reviews.review_page');
Route::post('/reviews', [ReviewController::class, 'store'])->name('reviews.store');

// Subscription Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/subscription/expired', [App\Http\Controllers\SubscriptionController::class, 'expired'])->name('subscription.expired');
});

Route::middleware(['auth', 'verified', 'check.subscription'])->group(function () {
   Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
   Route::get('/api-documentation', [ApiDocumentationController::class, 'index'])->name('api.documentation');

    // Customer Routes
    Route::post('/customers/import', [CustomerController::class, 'import'])->name('customers.import');
    Route::get('/customers/export', [CustomerController::class, 'export'])->name('customers.export');
    Route::get('/customers/sample', [CustomerController::class, 'sample'])->name('customers.sample');
    Route::resource('customers', CustomerController::class);

    // Tag Routes
    // Remove old tags and custom_fields resource routes
    // Route::resource('tags', TagController::class);
    // Route::resource('custom_fields', CustomFieldController::class);

    // New combined fields and tags routes
    Route::get('/fields-and-tags', [App\Http\Controllers\FieldsAndTagsController::class, 'index'])->name('fields-and-tags.index');
    Route::post('/fields-and-tags/field', [App\Http\Controllers\FieldsAndTagsController::class, 'storeField'])->name('fields-and-tags.storeField');
    Route::post('/fields-and-tags/tag', [App\Http\Controllers\FieldsAndTagsController::class, 'storeTag'])->name('fields-and-tags.storeTag');
    Route::put('/fields-and-tags/field/{field}', [App\Http\Controllers\FieldsAndTagsController::class, 'updateField'])->name('fields-and-tags.updateField');
    Route::put('/fields-and-tags/tag/{tag}', [App\Http\Controllers\FieldsAndTagsController::class, 'updateTag'])->name('fields-and-tags.updateTag');
    Route::delete('/fields-and-tags/field/{field}', [App\Http\Controllers\FieldsAndTagsController::class, 'destroyField'])->name('fields-and-tags.destroyField');
    Route::delete('/fields-and-tags/tag/{tag}', [App\Http\Controllers\FieldsAndTagsController::class, 'destroyTag'])->name('fields-and-tags.destroyTag');

    // Purchase Routes
    Route::get('/purchases', [PurchaseController::class, 'index'])->name('purchases.index');
    Route::get('/purchases/create', [PurchaseController::class, 'create'])->name('purchases.create');
    Route::post('/purchases', [PurchaseController::class, 'store'])->name('purchases.store');
   Route::post('/purchases/search-customer', [PurchaseController::class, 'searchCustomer'])->name('purchases.search.customer');
    Route::post('/purchases/create-customer', [PurchaseController::class, 'createCustomer'])->name('purchases.create.customer');

    // Loyalty Configuration Routes
    Route::get('/loyalty-configurations', [LoyaltyConfigurationController::class, 'index'])->name('loyalty_configurations.index');
   Route::post('/loyalty-configurations', [LoyaltyConfigurationController::class, 'store'])->name('loyalty_configurations.store');

   //Report Route
   Route::get('/reports', [ReportController::class, 'index'])->name('reports.index');

    // Campaign Routes
    Route::get('/campaigns', [CampaignController::class, 'index'])->name('campaigns.index');
    Route::post('/campaigns/send', [CampaignController::class, 'sendCampaign'])->name('campaigns.send');
    Route::post('/campaigns/get-filtered-customers', [CampaignController::class, 'getFilteredCustomers'])->name('campaigns.get.filtered.customers');

    Route::resource('templates', TemplateController::class)->middleware(['auth', 'verified']);

    //Settings Route
    Route::get('/settings', [SettingController::class, 'index'])->name('settings.index');
    Route::post('/settings/update-company', [SettingController::class, 'updateCompanyDetails'])->name('settings.update.company');
   Route::post('/settings/update-password', [SettingController::class, 'updatePassword'])->name('settings.update.password');
    Route::post('/settings/update-message', [SettingController::class, 'updateMessage'])->name('settings.update.message');
    Route::post('/settings/update-phone', [SettingController::class, 'updatePhoneNumber'])->name('settings.update.phone');

    //Customer Insights Route
    Route::get('/customer-insights', [CustomerInsightController::class, 'index'])->name('customer_insights.index');
   Route::post('/customer-insights/get-customers-by-tag', [CustomerInsightController::class, 'getCustomersByTag'])->name('customer_insights.get_customers_by_tag');
    Route::get('/customer-insights/export/{type}', [CustomerInsightController::class, 'export'])->name('customer_insights.export');

   //Enquiries Route
    Route::get('/enquiries/configure', [EnquiryQuestionController::class, 'index'])->name('enquiries.configure');
    Route::post('/enquiries/configure', [EnquiryQuestionController::class, 'store'])->name('enquiries.questions.store');
   Route::delete('/enquiries/configure/{enquiryQuestion}', [EnquiryQuestionController::class, 'destroy'])->name('enquiries.questions.destroy');
   Route::post('/enquiries/configure/order', [EnquiryQuestionController::class, 'updateOrder'])->name('enquiries.questions.updateOrder');
    Route::get('/enquiries/create', [EnquiryController::class, 'create'])->name('enquiries.create');
    Route::post('/enquiries', [EnquiryController::class, 'store'])->name('enquiries.store');
    Route::get('/enquiries/manage', [EnquiryController::class, 'manage'])->name('enquiries.manage');
   Route::get('/enquiries/{enquiry}', [EnquiryController::class, 'show'])->name('enquiries.show');
    Route::post('/enquiries/{enquiry}/convert', [EnquiryController::class, 'convertToCustomer'])->name('enquiries.convert');
    Route::delete('/enquiries/{enquiry}', [EnquiryController::class, 'destroy'])->name('enquiries.destroy');
    Route::post('/enquiries/{enquiry}/assign', [EnquiryController::class, 'assign'])->name('enquiries.assign');


    //Reviews Route
    Route::get('/reviews/panel', [ReviewController::class, 'index'])->name('reviews.index');

   //Front Desk Configuration
    Route::get('/frontdesk/configure', [FrontDeskController::class, 'configureCredentials'])->name('frontdesk.configure');
   Route::post('/frontdesk/configure', [FrontDeskController::class, 'storeCredentials'])->name('frontdesk.credentials.store');

   // Marketing Team Member Routes (Admin)
    Route::get('/marketingteams/create', [MarketingTeamMemberController::class, 'create'])->name('marketingteams.create');
    Route::post('/marketingteams', [MarketingTeamMemberController::class, 'store'])->name('marketingteams.store');
   Route::get('/marketingteams', [MarketingTeamMemberController::class, 'index'])->name('marketingteams.index');
   Route::delete('/marketingteams/{marketingTeamMember}', [MarketingTeamMemberController::class, 'destroy'])->name('marketingteams.destroy');

});

// Marketing Team Member Panel Routes (Separate Guard)
Route::prefix('marketing')->group(function () {
   Route::get('/login', [MarketingTeamMemberController::class, 'showLoginForm'])->name('marketing.login.show');
   Route::post('/login', [MarketingTeamMemberController::class, 'marketingLogin'])->name('marketing.login');
   Route::post('/logout', [MarketingTeamMemberController::class, 'logout'])->name('marketing.logout');


   Route::middleware(['auth:marketing'])->group(function () {
       Route::get('/dashboard', [MarketingTeamMemberController::class, 'dashboard'])->name('marketing.dashboard');
       Route::get('/enquiries', [MarketingTeamMemberController::class, 'enquiries'])->name('marketing.enquiries');
        Route::get('/enquiries/{enquiry}', [MarketingTeamMemberController::class, 'show'])->name('marketing.enquiries.show');
        Route::post('/enquiries/{enquiry}/convert', [MarketingTeamMemberController::class, 'convertToCustomer'])->name('marketing.enquiries.convert');
         Route::delete('/enquiries/{enquiry}', [MarketingTeamMemberController::class, 'destroyEnquiry'])->name('marketing.enquiries.destroy');


   });
});


require __DIR__.'/auth.php';

// Super Admin Routes
Route::prefix('admin')->name('super_admin.')->group(function () {
    Route::get('/login', [App\Http\Controllers\SuperAdmin\SuperAdminController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [App\Http\Controllers\SuperAdmin\SuperAdminController::class, 'login'])->name('login.post');

    Route::middleware('auth:super_admin')->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\SuperAdmin\SuperAdminController::class, 'dashboard'])->name('dashboard');
        Route::post('/logout', [App\Http\Controllers\SuperAdmin\SuperAdminController::class, 'logout'])->name('logout');
        
        // Subscription management routes
        Route::put('/users/{user}/subscription', [App\Http\Controllers\SuperAdmin\SuperAdminController::class, 'updateSubscription'])->name('users.subscription.update');
        Route::post('/users/{user}/extend', [App\Http\Controllers\SuperAdmin\SuperAdminController::class, 'extendSubscription'])->name('users.subscription.extend');
        Route::post('/users/{user}/toggle-status', [App\Http\Controllers\SuperAdmin\SuperAdminController::class, 'toggleUserStatus'])->name('users.toggle.status');
    });
});
