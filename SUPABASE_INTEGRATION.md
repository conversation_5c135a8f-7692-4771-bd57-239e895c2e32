# Supabase Storage Integration

This document explains the Supabase storage integration implemented in the Customer Management System.

## Overview

The system now uses Supabase storage as the primary storage solution for:
- Template images (generated previews)
- Campaign media files (images, videos, documents)
- User uploaded images in templates

## Configuration

### Environment Variables

Add the following to your `.env` file:

```env
SUPABASE_URL=https://fhgowvbaaviyizgwwuyd.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZoZ293dmJhYXZpeWl6Z3d3dXlkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5NTE1MzMsImV4cCI6MjA2NzUyNzUzM30.Y5FCIT7EHQUoA7mgVvTlqfWtxG7MpTbmDiTXChg2K-w
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZoZ293dmJhYXZpeWl6Z3d3dXlkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTk1MTUzMywiZXhwIjoyMDY3NTI3NTMzfQ.QDSfWidkZYG76jPaTqSCklq9WI0nnepFWbZPljnodjI
SUPABASE_BUCKET_NAME=cms-storage
```

## Setup

### 1. Run Database Migration

```bash
php artisan migrate
```

This will add the `content_image_supabase_url` column to the templates table.

### 2. Initialize Supabase Bucket

```bash
php artisan supabase:init-bucket
```

This command will create the storage bucket in Supabase if it doesn't exist.

## Features

### Dual Storage Strategy

The system implements a dual storage strategy:
1. **Primary**: Supabase storage (cloud-based, globally accessible)
2. **Fallback**: Local storage (for backup and development)

### Template Storage

When creating or updating templates:
- Template preview images are generated locally using Browsershot
- Images are then uploaded to Supabase storage
- Both local and Supabase URLs are stored in the database
- The `getImageUrl()` method prioritizes Supabase URLs

### Campaign Media Storage

When sending campaigns with media:
- Media files are uploaded to Supabase storage first
- If Supabase upload fails, falls back to local storage
- WhatsApp API receives the Supabase URL for better delivery

## API Integration

### WhatsApp API

The system sends media URLs from Supabase to the ConnectEzee WhatsApp API:

```php
// Template messages use Supabase URLs
$imageUrl = $template->getImageUrl(); // Returns Supabase URL if available

// Campaign media uses Supabase URLs
$supabaseResult = $supabaseService->uploadFile($media, 'campaign-media');
if ($supabaseResult['success']) {
    $mediaUrl = $supabaseResult['url']; // Supabase URL
}
```

## File Structure

```
app/
├── Services/
│   └── SupabaseStorageService.php    # Main Supabase service
├── Console/Commands/
│   └── InitializeSupabaseBucket.php  # Bucket initialization command
├── Models/
│   └── Template.php                  # Enhanced with getImageUrl() method
└── Http/Controllers/
    ├── TemplateController.php        # Updated for Supabase storage
    └── CampaignController.php        # Updated for Supabase storage
```

## Storage Paths

- **Template Images**: `template_renders/template_{user_id}_{timestamp}.png`
- **Template User Uploads**: `template_images/{filename}`
- **Campaign Media**: `campaign-media/{uuid}_{filename}`

## Benefits

1. **Global Accessibility**: Files stored in Supabase are accessible worldwide
2. **Reliability**: Cloud storage with built-in redundancy
3. **Performance**: CDN-backed delivery for faster loading
4. **Scalability**: No local storage limitations
5. **Backup Strategy**: Local storage serves as fallback

## Troubleshooting

### Upload Failures

If Supabase uploads fail:
1. Check internet connectivity
2. Verify Supabase credentials in `.env`
3. Ensure bucket exists (run `php artisan supabase:init-bucket`)
4. Check Supabase service status

### Missing Images

If images don't display:
1. Check if `content_image_supabase_url` is populated in database
2. Verify Supabase bucket is public
3. Test direct URL access to Supabase files

### Local Development

For local development without Supabase:
- System will automatically fall back to local storage
- All functionality remains intact
- Images will be served from local storage

## Security

- Service role key is used for uploads (write access)
- Anon key can be used for public reads
- Bucket is configured as public for image delivery
- File paths include user IDs for basic access control
